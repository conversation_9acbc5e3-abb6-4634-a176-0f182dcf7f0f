<template>
  <span class="gradient-text" :style="gradientStyle">
    {{ text }}
  </span>
</template>

<script setup>
import { computed } from "vue";

// 定义 props
const props = defineProps({
  text: {
    type: String,
    required: true,
  },
  colors: {
    type: Array,
    default: () => ["#ff0000", "#00ff00"], // 默认红色渐变到绿色
  },
  angle: {
    type: Number,
    default: 0, // 默认 0 度（从上到下）
  },
});

// 计算渐变样式
const gradientStyle = computed(() => {
  const colorString = props.colors.join(", ");
  return {
    background: `linear-gradient(${props.angle}deg, ${colorString})`,
    "-webkit-background-clip": "text",
    "-webkit-text-fill-color": "transparent",
    "background-clip": "text",
  };
});
</script>

<style lang="scss" scoped>
.gradient-text {
  display: inline-block;
  font-weight: bold;
  font-size: inherit;
  line-height: inherit;

  // 确保渐变效果在不同浏览器中正常显示
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
