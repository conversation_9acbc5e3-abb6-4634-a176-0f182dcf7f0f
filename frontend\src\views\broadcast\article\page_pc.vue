<template>
  <div
    style="
      background:
        radial-gradient(at 20% 0%, rgba(202, 229, 245, 0.6), transparent 20%),
        radial-gradient(at 60% -15%, rgba(202, 229, 245, 0.7), transparent 40%),
        radial-gradient(
          at 100% 100%,
          rgba(160, 235, 248, 0.7),
          transparent 40%
        ),
        radial-gradient(at 0% 100%, rgb(243, 252, 202), transparent 30% 30%);
    ">
    <div
      @click="handleShowCommentDrawer"
      style="
        position: fixed;
        top: calc(var(--navbar-height) + 167px + 70px);
        left: 160px;

        width: 40px;
        height: 40px;
        background: #ffffff;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        /* border: 1px solid #000; */
      ">
      <img
        src="@/assets/images/icon27.png"
        alt=""
        srcset=""
        style="width: 16px" />
    </div>
    <div
      @click.stop="handleCollect"
      style="
        position: fixed;
        top: calc(var(--navbar-height) + 167px + 140px);
        left: 160px;

        width: 40px;
        height: 40px;
        background: #ffffff;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        /* border: 1px solid #000; */
      ">
      <Button1
        class="meta-favorite-icon"
        width="18px"
        height="18px"
        gray
        :filled="article.is_collected"></Button1>
    </div>

    <div class="article">
      <div class="top">
        <div class="width" style="margin: 0 auto">
          <div class="title">
            {{ article.title }}
          </div>
          <div class="summary">
            {{ article.summary }}
          </div>
          <div class="meta">
            <img src="@/assets/images/icon25.png" />
            <span>
              {{ dayjs(article.created_at).format("YYYY/MM/DD") }}
            </span>
            <img src="@/assets/images/icon26.png" />
            <span>{{ article.read_count }}</span>
            <img src="@/assets/images/icon27.png" />
            <span>{{ article.comment_count }}</span>
            <div class="meta-favorite" @click.stop="handleCollect">
              <Button1
                class="meta-favorite-icon"
                width="18px"
                height="18px"
                gray
                :filled="article.is_collected"></Button1>
              <span>{{ article.collect_count }}</span>
            </div>
          </div>
          <div class="tag" v-if="article.tags">
            文章标签:
            <div
              class="tag-item"
              v-for="(item, index) in article.tags"
              :key="index"
              @click="handleTags(item)">
              {{ item }}
            </div>
          </div>
        </div>
      </div>
      <div
        style="
          min-height: calc(100vh - var(--navbar-height) - 152px - 40px - 16px);
        ">
        <div
          style="
            width: fit-content;
            position: relative;
            margin: 16px auto 0 auto;
          ">
          <div class="article-detail" @scroll="scroll" ref="article-detail">
            <el-backtop
              :right="40"
              :bottom="40"
              :visibility-height="200"
              target=".article-detail"
              @click="backTop" />
            <MarkdownPreview
              class="content"
              :text="article.content"
              style=""></MarkdownPreview>
          </div>
        </div>
        <div
          class="related-projects"
          v-if="article.related_projects && article.related_projects.length">
          <div class="title">
            <img src="@/assets/images/icon28.png" />
            <span>关联项目</span>
          </div>
          <div class="cont">
            <div
              class="item"
              v-for="(item, index) in article.related_projects"
              :key="index"
              @click="
                handleLink(
                  checkJson(item) ? checkJson(item).url : checkJson(item)
                )
              ">
              <div class="summary">{{ checkJson(item).summary }}</div>
              <div class="title">{{ checkJson(item).title }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <HomeFooter style="background-color: transparent"></HomeFooter>
    <Share
      style="position: fixed; right: 40px; bottom: 60px"
      :project="article"></Share>
    <WxQrCode />
    <el-drawer
      class="comment-drawer"
      v-model="drawer"
      title="评论"
      direction="rtl">
      <div>
        <div class="comment-drawer-content">
          <el-mention
            ref="textareaRef"
            class="textarea"
            v-model="textarea"
            :autosize="{ minRows: 3, maxRows: 8 }"
            maxlength="1000"
            type="textarea"
            show-word-limit
            placeholder="发表评论"
            :options="options"
            @focus="focus"
            @blur="blur"
            @input="handleInput" />
          <img
            class="emoji"
            src="@/assets/images/guguda.png"
            style="left: 40px"
            @click.stop="handleAiTe" />
          <el-popover
            placement="bottom"
            :width="306"
            popper-class="emoji-popover">
            <div>
              <EmojiPicker
                :hide-search="true"
                :native="true"
                @select="onSelectEmoji"
                :disabled-groups="[
                  'travel_places',
                  'flags',
                  'symbols',
                  'objects',
                  'activities',
                ]"
                :hide-group-names="true" />
            </div>
            <template #reference>
              <img class="emoji" src="@/assets/images/emoji.png" />
            </template>
          </el-popover>

          <div class="btns">
            <el-button
              class="button"
              type="primary"
              :disabled="disabled"
              @click.stop="handleSend">
              发送
            </el-button>
          </div>
        </div>
        <div class="comment-container">
          <comment
            :data="msgList"
            :sendName="''"
            :articleId="query.id"
            :rootIds="rootIds"
            :commentIndex="commentIndex" />
          <div
            v-if="commentIndex.rootIndex > 3"
            @click="handleShowAllComment"
            style="
              display: flex;
              justify-content: center;
              align-items: center;
              height: 52px;
              margin-top: 12px;
              border-radius: 4px;
              background-color: #fff;
              margin: 0 15px 12px;
            ">
            <span>查看全部评论</span>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import Share from "@/components/Share/index.vue";
import WxQrCode from "@/components/WxQrCode";
import EmojiPicker from "vue3-emoji-picker";
import "vue3-emoji-picker/css";
import Button1 from "@/components/Button/index1.vue";
import HomeFooter from "@/layout/components/HomeFooter/index.vue";
import { getArticleList, commentTree, commentCreat } from "@/api/article";
import { addUserCollect, deleteUserCollect } from "@/api/broadcast";
import useUserStore from "@/store/modules/user";
import dayjs from "dayjs";
import comment from "@/views/broadcast/article/comment.vue";
import { getToken } from "@/utils/auth";
import { ElMessage } from "element-plus";
import MarkdownPreview from "@/components/MarkdownPreview";
import { getCurrentInstance, nextTick, useTemplateRef } from "vue";
import { useThrottleFn } from "@vueuse/core";

const { query } = useRoute();
const article = ref({});
const userStore = useUserStore();
const textareaRef = useTemplateRef("textareaRef");
const articleDetail = useTemplateRef("article-detail");
const isScrolling = ref(false);
const msgList = ref([]);
const drawer = ref(false); // 抽屉是否显示
const color = ref("#e4e6eb"); // 输入框背景颜色
const disabled = ref(true); // 输入框是否禁用

const options = ref([
  {
    label: "咕咕答",
    value: "咕咕答",
  },
]);

const handleShowCommentDrawer = () => {
  drawer.value = true;
};

const initArticle = () => {
  getArticleList({
    article_id: query.id,
  }).then((res) => {
    if (res.code === 200) {
      article.value = res.data;
    }
  });
  getComment();
};
const rootIds = ref([]); // 存储所有根节点的id
const commentIndex = ref({}); // 存储每个根节点的数量
const flattenTree = (tree, parentId = null, level = 0) => {
  let objIndex = {
    rootIndex: 0,
  };
  const traverse = (node, parentId, level) => {
    if (node.root_id === node.id) {
      node.index = objIndex.rootIndex;
      objIndex.rootIndex += 1;
      rootIds.value.push(node.id);
    } else {
      if (objIndex[node.root_id] === undefined) {
        objIndex[node.root_id] = 0;
      }
      node.index = objIndex[node.root_id];
      objIndex[node.root_id] += 1;
    }

    // 递归处理子节点
    if (node.replies && node.replies.length > 0) {
      node.replies.forEach((child) => {
        traverse(child, node.id, level + 1);
      });
    }
  };

  // 如果 tree 是数组，则遍历每个根节点
  if (Array.isArray(tree)) {
    tree.forEach((root, index) => traverse(root, null, 0, index));
  } else {
    // 如果 tree 是单个根节点
    traverse(tree, null, 0, index);
  }
  commentIndex.value = objIndex;
};

const getComment = () => {
  commentTree({
    article_id: query.id,
    page: 1,
    page_size: 50,
    depth: 10,
  }).then((res) => {
    if (res.code === 200) {
      flattenTree(res.data.comments);
      msgList.value = res.data.comments;
    }
  });
};

const textarea = ref("");

const handleShowComment = () => {
  if (getToken()) {
    userStore.showCommentInput = true;
    nextTick(() => {
      articleDetail.value.scrollTop = articleDetail.value.scrollHeight;
    });
    userStore.replyName = "";
    textarea.value = "";
  } else {
    ElMessage({
      message: "请登录后评论",
      type: "warning",
    });
    userStore.loginDialogVisible = true;
  }
};

const handleCancel = () => {
  userStore.showCommentInput = false;
};

/**
 *滚到底部
 */
const scroll = useThrottleFn(() => {
  if (!isScrolling.value) {
    document.getElementsByClassName("app-main")[0].scroll({ top: 999 });
  }
}, 1000);

/**
 * 回到顶部
 */
const backTop = () => {
  articleContainer.value.scroll({ top: 0, behavior: "smooth" });
  isScrolling.value = true;
  const timer = setTimeout(() => {
    isScrolling.value = false;
    clearTimeout(timer);
  }, 1000);
};

const handleSend = () => {
  commentCreat({
    content: textarea.value,
    parent_id: "",
    project_id: query.id,
  }).then((res) => {
    if (res.code === 200) {
      userStore.showCommentInput = false;

      textarea.value = "";
      userStore.refreshComment = true;
      const timer = setTimeout(() => {
        userStore.refreshComment = false;
        clearTimeout(timer);
      }, 1000);
    }
  });
};

/**
 * 处理json字符串报错
 * @param str json字符串
 */
const checkJson = (str) => {
  try {
    return JSON.parse(str);
  } catch (error) {
    return "";
  }
};

const { proxy } = getCurrentInstance();
/**
 * 用户收藏
 */
const handleCollect = async () => {
  // 判断用户是否登录，如果为登录，则提示用户去登录
  if (!userStore?.name) {
    proxy.$modal.msgWarning("登录后即可收藏");
    return;
  }
  let res = {};
  if (article.value.is_collected) {
    res = await deleteUserCollect({ article_id: article.value.id });
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已取消收藏");
      article.value.is_collected = !article.value.is_collected;
      article.value.collect_count--;
    }
  } else {
    res = await addUserCollect({ article_id: article.value.id });
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已收藏");
      article.value.is_collected = !article.value.is_collected;
      article.value.collect_count++;
    }
  }
};

const router = useRouter();
/**
 * 标签跳转到首页进行搜索
 * @param item
 */
const handleTags = (item) => {
  router.push({
    path: "/",
    query: { search: item, tab: "zixun" },
  });
};

const handleLink = (url) => {
  if (url) {
    window.open(url);
  }
};

const focus = () => {
  color.value = "#1e80ff";
};

const blur = () => {
  color.value = "#e4e6eb";
};

const handleInput = () => {
  if (textarea.value) {
    disabled.value = false;
  } else {
    disabled.value = true;
  }
};
const handleAiTe = () => {
  textarea.value = textarea.value + "@咕咕答";
};

const onSelectEmoji = (emoji) => {
  textarea.value = textarea.value + emoji.i;
};
watch(
  () => userStore.refreshComment,
  (newVal) => {
    if (newVal) {
      getComment();
    }
  }
);
onMounted(() => {
  userStore.showCommentInput = false;
  initArticle();
  if (query.commentId) {
    setTimeout(() => {
      document
        .getElementById(query.commentId)
        .scrollIntoView({ block: "center" });
    }, 500);
  }
});

const handleShowAllComment = () => {
  userStore.allMsgStatus["root"] = 99;
};

//  初始化位置
</script>
<style scoped lang="scss">
::v-deep(.v3-emoji-picker .v3-footer) {
  display: none;
}
::v-deep(.v3-emoji-picker .v3-header) {
  display: none;
}
::v-deep(.v3-emoji-picker) {
  box-shadow: none;
}
.article {
  .width {
    width: var(--content-max-width);
  }
  .meta {
    display: flex;
    align-items: center;
    margin-top: 4px;
    font-size: 14px;
    color: #656464;
    img {
      width: 16px;
      margin-right: 5px;
    }

    span {
      margin-right: 25px;
    }

    .meta-favorite {
      margin-left: 20px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font-size: 14px;
      font-weight: normal;
      color: #646464;
      height: 100%;
      width: fit-content;
      cursor: pointer;
      .meta-favorite-icon {
        width: 18px;
        height: 18px;
        margin-right: 4px;
      }
      .meta-favorite-text {
        position: relative;
        top: 1px;
      }
    }
  }
  .top {
    min-height: 143px;
    padding: 32px 0;
    background: #eef0f1;

    .title {
      width: 80%;
      white-space: nowrap; /* 禁止换行 */
      overflow: hidden; /* 隐藏溢出内容 */
      text-overflow: ellipsis; /* 显示省略号 */
      font-size: 18px;
      color: #4c6d7d;
      font-weight: 800;
    }
    .summary {
      font-weight: 400;
      font-size: 14px;
      color: #3f4a54;
      margin-top: 10px;
    }

    .time {
      margin-top: 12px;
      font-size: 14px;
      color: #656464;
    }

    .tag {
      margin-top: 10px;
      display: flex;
      flex-wrap: wrap;
      gap: 4px 16px;
      font-size: 12px;
      color: #60848d;
      .tag-item {
        padding: 2px 18px;
        background: #d9ecf0;
        border-radius: 4px 4px 4px 4px;
        cursor: pointer;
      }
    }
  }

  .article-detail {
    padding: 15px;
    width: var(--content-max-width);
    max-height: calc(100vh - var(--navbar-height) - 40px - 40px - 144px - 16px);
    overflow: auto;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 8px 8px 0px 0px;
    border: 1px solid #e6e6e6;

    font-size: 14px;
    color: #000000;
    scrollbar-gutter: stable;

    ::v-deep(.el-backtop) {
      --el-backtop-bg-color: transparent !important;
      position: absolute;
    }

    .comment-container {
      margin-top: 15px;
      background: #f4f4f4;
      border-radius: 4px;
    }
  }
}

.related-projects {
  width: var(--content-max-width);
  font-size: 16px;
  color: #000000;
  margin: 20px auto;

  .title {
    display: flex;
    align-items: center;

    img {
      width: 20px;
      margin-right: 5px;
    }
  }

  .cont {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .item {
      width: 313px;
      height: 77px;
      background: #fafbfb;
      border-radius: 4px 4px 4px 4px;
      padding: 15px 20px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .summary {
        font-size: 14px;
        color: #000000;
        cursor: pointer;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      .title {
        font-size: 14px;
        color: #000000;
        font-weight: 800;
        cursor: pointer;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      &:hover {
        background-color: #e8e8e8;
      }
    }
  }
}
</style>

<style>
.comment-drawer {
  .el-drawer__header {
    margin-bottom: 0;
    padding-bottom: 20px;
    border-bottom: 1px solid #e4e6eb;
    font-weight: 800;
    font-size: 18px;
  }

  .comment-drawer-content {
    position: relative;
    padding: 6px;
    outline: 1px solid v-bind(color);
    border-radius: 4px;

    .emoji {
      width: 20px;
      position: absolute;
      bottom: 10px;
      left: 6px;
      cursor: pointer;
    }

    .textarea {
      width: 100%;
      margin-top: 5px;
      border-radius: 5px;
      font-size: 14px;

      textarea {
        resize: none;
        box-shadow: none;
      }

      .el-input__count {
        bottom: -25px;
        right: 80px;
      }
    }

    .btns {
      display: flex;
      flex-direction: row-reverse;

      /* .button {
        width: 60px;
        height: 32px;
        margin-top: 5px;
        border-radius: 4px;
        background-color: #1e80ff;
        font-size: 14px;
        color: #ffffff;
        text-align: center;
        line-height: 32px;
        border: none;
        cursor: pointer;
      } */
    }
  }
}
</style>
