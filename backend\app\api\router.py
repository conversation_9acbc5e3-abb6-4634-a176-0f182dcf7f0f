"""
API路由配置
"""
import os
from typing import List, Optional, Dict, Any
from tornado.web import URLSpec
from pydantic import BaseModel, Field

from app.api.base import CORSStaticFileHandler
from app.api.v1.article import Article<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ArticleCommentCreate<PERSON><PERSON><PERSON>, ArticleCommentAuditHandler
from app.api.v1.article.article import <PERSON><PERSON>and<PERSON>, ArticleLikeHandler, ArticleShareHandler, \
    ArticleCommentSearchHandler
from app.api.v1.article.article_collect import ArticleCollectHandler, ArticleCollectCheckHandler
from app.api.v1.article.comment_article import ArticleCommentDeleteHandler
from app.api.v1.article.proxy_article import ProxyShowHandler
from app.api.v1.article.wechat_article import <PERSON><PERSON>t<PERSON>rt<PERSON><PERSON>and<PERSON>, WechatArticleDetailHandler, \
    WechatArticlesStatsHandler

from app.api.v1.github.github_image import GitH<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GitHubImageListHandler
from app.api.v1.github.github_project import GitHubProjectD<PERSON><PERSON><PERSON><PERSON><PERSON>, GitHubProjectHandler, \
    GitHubProjectCardGenerateHandler, ProjectUserCollectHandler, MixedProjectArticleHandler
from app.api.v1.github.github_project_agent_remote import AnalyzerCalledHandler, AgentHealthCheckHandler, \
    PublisherCalledHandler, AnalyzerCancelHandler
from app.api.v1.github.github_project_card import GitHubCardHandler, GitHubCardInteractionHandler, \
    GitHubCardDetailHandler, GitHubCardBatchHandler, GitHubDownloadStatusHandler, GitHubGenerateStatusHandler, \
    GitHubAutoFinderHandler, GitHubSolveDataHandler
from app.api.v1.github.github_project_detail import GitHubProjectDetailHandler
from app.api.v1.github.github_project_file import GitHubProjectFileHandler
from app.api.v1.github.github_project_scan_workflow import ProjectScanHandler, ProjectGetFlowStatusHandler, \
    ProjectLinkCheckHandler, ProjectDownloadHandler, ProjectPriorityHandler, ProjectCancelGenerationHandler, \
    ProjectUserFlowLog, ProjectUserHistoryLog, ProjectGetFlowStatusCurrentUserHandler
from app.api.v1.github.github_project_share import GitHubShareHandler, GitHubPhoneShareSolveHandler
from app.api.v1.health import HealthHandler
from app.api.v1.rbac.auth import (
    LoginHandler,
    LogoutHandler,
    RefreshTokenHandler, FastAuthHandler, FastAuthVerificationCodeHandler, BindChangeAuthHandler,
    BindChangeAuthVerificationCodeHandler, BindChangeOriginalAuthHandler, BindChangeAuthVerificationCodeOriginalHandler
)
from app.api.v1.rbac.user import (
    UserHandler,
    UserDetailHandler,
    RegisterHandler,
    ResetPasswordHandler,
    UserStatusHandler,
    UserPermissionsHandler, VerificationCodeHandler, UpdateSelfPasswordHandler,
    UpdateSelfPasswordVerificationCodeHandler, UpdateOauthEmailVerificationCodeHandler, UpdateOauthEmailHandler
)
from app.api.v1.rbac.role import RoleHandler, RoleDetailHandler, RolePermissionGroupHandler, RolePermissionHandler, \
    UserRoleHandler
from app.api.v1.rbac.permission import PermissionHandler, PermissionDetailHandler
from app.api.v1.rbac.permission_group import PermissionGroupHandler, PermissionGroupDetailHandler
from app.api.v1.rbac.oauth import (
    OAuthProvidersHandler,
    OAuthAuthorizeHandler,
    OAuthGithubCallbackHandler,
    OAuthAccountsHandler,
    OAuthAccountDeleteHandler, OAuthGiteeCallbackHandler,
)
from app.api.v1.rbac.current_user import CurrentUserHandler, CurrentUserChangePasswordHandler, \
    CurrentUserSystemLogHandler, GitHubCardSearchHandler, CurrentUserSystemLogCommentReplyHandler
from app.api.v1.git import (
    GitRepositoryHandler,
    GitBranchesHandler,
    GitCommitsHandler,
    GitFilesHandler,
    GitFileContentHandler,
    GitFileHistoryHandler,
    GitFindRepositoriesHandler
)
from app.api.v1.rbac.user import (
    PasswordResetRequestHandler,
    PasswordResetCompleteHandler
)
from app.api.v1.github.github_project_tree import GitHubProjectTreeHandler
from app.api.v1.github.github_search import RepoUrlValidationHandler
from app.api.v1.rbac.wechat import WechatVerifyCodeHandler, WechatOfficialAccountAuthHandler
from app.api.v1.statistics import StatisticsHandler, StatisticsAdminHandler
from app.api.v1.intelligent_article import get_intelligent_article_routes

from app.api.v1.universal_search.search import (
    UniversalSearchHandler,
    ArticleSearchHandler,
    ProjectSearchHandler,
    SearchStatsHandler
)
from app.api.v1.hot_topics.hot_topics import (
    HotTopicsQueryHandler,
    HotTopicsFetchStoreHandler,
    NodesPageHandler,
    NodesAllHandler,
    NodeLatestHandler,
    NodeHistoryHandler,
    SearchHandler,
    HotBoardHandler,
)
from app.api.v1.ai_chat import (
    ChatHandler, ChatStreamHandler,
    SessionHandler, SessionListHandler, SessionDetailHandler,
    ModelListHandler, ModelConfigHandler, ModelProviderHandler,
    ToolsListHandler, MCPServerHandler, ToolsConfigHandler, ToolCallHandler
)
from app.api.v1.ai_chat.session import SessionClearHandler
from app.api.v1.ai_chat.prompt_template import (
    PromptTemplateCollectionHandler,
    PromptTemplateItemHandler,
    PromptTemplateFavoriteHandler,
    PromptTemplateCloneHandler,
    PromptTemplateRenderHandler,
)
from app.api.v1.reference import (
    ReferenceCollectionHandler,
    ReferenceBatchHandler,
    ReferenceItemHandler,
)

from app.api.v1.article.gugu_mention import GuguAnalysisHandler, GuguReplyHandler

from app.api.v1.github.github_project_logictree import (
    LogicTreeRewriteListHandler,
    LogicTreeProjectCardsHandler,
    LogicTreeModelListHandler,
    LogicTreeRewriteHandler, LogicTreeAssociatedProjectsHandler, LogicTreeTrendingHandler
)


def setup_routes() -> List[URLSpec]:
    """
    设置API路由
    
    Returns:
        List[URLSpec]: 路由处理器列表
    """
    # v1版本API路由
    v1_prefix = "/api/v1"
    v1_routes = [
        # 健康检查
        (f"{v1_prefix}/health", HealthHandler),
        
        # 认证相关
        (f"{v1_prefix}/auth/login", LoginHandler),
        (f"{v1_prefix}/auth/refresh", RefreshTokenHandler),
        (f"{v1_prefix}/auth/logout", LogoutHandler),
        (f"{v1_prefix}/users/verification-code", VerificationCodeHandler),

        (f"{v1_prefix}/auth/fastauth", FastAuthHandler),
        (f"{v1_prefix}/auth/fastauth-verification-code", FastAuthVerificationCodeHandler),

        (f"{v1_prefix}/auth/bind-change", BindChangeAuthHandler),
        (f"{v1_prefix}/auth/bind-change-verification-code", BindChangeAuthVerificationCodeHandler),

        (f"{v1_prefix}/auth/bind-change-original", BindChangeOriginalAuthHandler),
        (f"{v1_prefix}/auth/bind-change-verification-code-original", BindChangeAuthVerificationCodeOriginalHandler),

        # OAuth认证
        (f"{v1_prefix}/oauth/providers", OAuthProvidersHandler),
        (f"{v1_prefix}/oauth/authorize", OAuthAuthorizeHandler),
        (f"{v1_prefix}/oauth/callback/github", OAuthGithubCallbackHandler),
        (f"{v1_prefix}/oauth/callback/gitee", OAuthGiteeCallbackHandler),
        (f"{v1_prefix}/oauth/accounts", OAuthAccountsHandler),
        (f"{v1_prefix}/oauth/accounts/([^/]+)", OAuthAccountDeleteHandler),

        # auth 微信服务号
        # 微信获取验证码
        (f"{v1_prefix}/wechat/verify-code", WechatVerifyCodeHandler),

        # 微信服务器认证
        (f"{v1_prefix}/wechat/connect", WechatOfficialAccountAuthHandler),

        # 用户管理 注意users如果没有二级地址会统一适配成UserDetailHandler
        (f"{v1_prefix}/users", UserHandler),
        (f"{v1_prefix}/users/register", RegisterHandler),
        (f"{v1_prefix}/users/verification-code", VerificationCodeHandler),
        (f"{v1_prefix}/users/([^/]+)", UserDetailHandler),
        # (f"{v1_prefix}/users/([^/]+)/change-password", ChangePasswordHandler),
        (f"{v1_prefix}/users/([^/]+)/reset-password", ResetPasswordHandler),
        (f"{v1_prefix}/users/([^/]+)/status/([^/]+)", UserStatusHandler),
        (f"{v1_prefix}/users/([^/]+)/permissions", UserPermissionsHandler),

        # 为什么这里要重新实现一次？
        (f"{v1_prefix}/users/password-reset/request", PasswordResetRequestHandler),
        (f"{v1_prefix}/users/password-reset/complete", PasswordResetCompleteHandler),
        (f"{v1_prefix}/users/update-self-password/verification-code", UpdateSelfPasswordVerificationCodeHandler),
        (f"{v1_prefix}/users/update-self-password/complete", UpdateSelfPasswordHandler),
        (f"{v1_prefix}/users/update-oauth-email/verification-code", UpdateOauthEmailVerificationCodeHandler),
        (f"{v1_prefix}/users/update-oauth-email/complete", UpdateOauthEmailHandler),

        # 角色管理
        (f"{v1_prefix}/roles", RoleHandler),
        (f"{v1_prefix}/user-roles", UserRoleHandler),
        (f"{v1_prefix}/roles/([^/]+)", RoleDetailHandler),
        (f"{v1_prefix}/roles/([^/]+)/permission-groups", RolePermissionGroupHandler),
        (f"{v1_prefix}/roles/([^/]+)/permissions", RolePermissionHandler),
        
        # 权限管理
        (f"{v1_prefix}/permissions", PermissionHandler),
        (f"{v1_prefix}/permissions/([^/]+)", PermissionDetailHandler),
        
        # 权限组管理
        (f"{v1_prefix}/permission-groups", PermissionGroupHandler),
        (f"{v1_prefix}/permission-groups/([^/]+)", PermissionGroupDetailHandler),
        
        # 当前用户相关
        (f"{v1_prefix}/current-user", CurrentUserHandler),
        (f"{v1_prefix}/current-user/change-password", CurrentUserChangePasswordHandler),
        (f"{v1_prefix}/current-user/cards-search", GitHubCardSearchHandler),

        # Git仓库管理
        (f"{v1_prefix}/git/repository", GitRepositoryHandler),
        (f"{v1_prefix}/git/branches", GitBranchesHandler),
        (f"{v1_prefix}/git/commits", GitCommitsHandler),
        (f"{v1_prefix}/git/files", GitFilesHandler),
        (f"{v1_prefix}/git/file-content", GitFileContentHandler),
        (f"{v1_prefix}/git/file-history", GitFileHistoryHandler),
        (f"{v1_prefix}/git/find-repositories", GitFindRepositoriesHandler),

        # github下载和转换

        (f"{v1_prefix}/github/project", GitHubProjectDetailHandler),
        (f"{v1_prefix}/github/projects", GitHubProjectHandler),
        #混合搜索新 测试修改提交
        (f"{v1_prefix}/projects/mixed", MixedProjectArticleHandler),

        (f"{v1_prefix}/github/projects/generate", GitHubProjectCardGenerateHandler),
        (f"{v1_prefix}/github/projects/download", GitHubProjectDownloadHandler),
        (f"{v1_prefix}/github/projects/generate-cards", GitHubProjectCardGenerateHandler),
        (f"{v1_prefix}/github/projects/trees", GitHubProjectTreeHandler),
        (f"{v1_prefix}/github/projects/cards", GitHubCardHandler),
        (f"{v1_prefix}/github/projects/cards/detail", GitHubCardDetailHandler),
        (f"{v1_prefix}/github/projects/cards/batch", GitHubCardBatchHandler),  # 批量修改 先删再加
        (f"{v1_prefix}/github/projects/file", GitHubProjectFileHandler),
        (f"{v1_prefix}/github/projects/cards/action", GitHubCardInteractionHandler),
        (f"{v1_prefix}/github/upload/image", GitHubImageUploadHandler),
        (f"{v1_prefix}/github/projects/images", GitHubImageListHandler),

        # GitHub下载/AI生成统计API
        (f"{v1_prefix}/github/projects/dowloadstatus", GitHubDownloadStatusHandler),
        (f"{v1_prefix}/github/projects/generatestatus", GitHubGenerateStatusHandler),
        (f"{v1_prefix}/github/projects/needgenerateadder", GitHubAutoFinderHandler),
        (f"{v1_prefix}/github/projects/githubsolvedata", GitHubSolveDataHandler),

        (f"{v1_prefix}/github/validate-repo", RepoUrlValidationHandler),
        (f"{v1_prefix}/statistics", StatisticsHandler),
        (f"{v1_prefix}/statistics/admin", StatisticsAdminHandler),


        # 一个用户4个表 收藏/历史/流程 系统

        # github 收藏api
        (f"{v1_prefix}/github/projects/user-collect", ProjectUserCollectHandler),
        # github 流程日志api
        (f"{v1_prefix}/github/projects/user-flow-log", ProjectUserFlowLog),
        # github 浏览日志api
        (f"{v1_prefix}/github/projects/user-access-log", ProjectUserHistoryLog),
        # github 系统消息api 列表 已读 未读
        (f"{v1_prefix}/github/projects/system-message-log", CurrentUserSystemLogHandler),
        # 新增reply 评论消息要跟系统消息分开
        (f"{v1_prefix}/github/projects/system-message-log-comment-reply", CurrentUserSystemLogCommentReplyHandler),

        # github 分享api
        # 统计+1
        (f"{v1_prefix}/github/projects/share/statistics", GitHubShareHandler),
        # post修改 get获取
        (f"{v1_prefix}/github/projects/share/phone", GitHubPhoneShareSolveHandler),

        # 扫描项目流程 V
        (f"{v1_prefix}/githubflow/projects/scan", ProjectScanHandler),
        # 判断是否合法 post V
        (f"{v1_prefix}/githubflow/projects/validatelink", ProjectLinkCheckHandler),
        # 项目下载  post downloader中要带访问信息和访问人X
        (f"{v1_prefix}/githubflow/projects/normaldownload", ProjectDownloadHandler),
        # 项目加急 post V
        (f"{v1_prefix}/githubflow/projects/fastsolve", ProjectPriorityHandler),
        # 取消分析 post
        (f"{v1_prefix}/githubflow/projects/cancelgeneration", ProjectCancelGenerationHandler),
        # 获取下载-分析状态 get
        (f"{v1_prefix}/githubflow/projects/getflowstatus", ProjectGetFlowStatusHandler),
        # 获取下载-分析状态 get
        (f"{v1_prefix}/githubflow/projects/getflowstatus-current", ProjectGetFlowStatusCurrentUserHandler),

        # remote AI agent
        (f"{v1_prefix}/remote/agent/analyzer-called", AnalyzerCalledHandler),
        (f"{v1_prefix}/remote/agent/publisher-called", PublisherCalledHandler),
        (f"{v1_prefix}/remote/agent/health-check", AgentHealthCheckHandler),
        (f"{v1_prefix}/remote/agent/cancel-analysis", AnalyzerCancelHandler),
        # 获取某个用户的访问记录
        # 创建项目的时候记录
        # 可能要有专门的service
        # 先理清下载记录

        # 文章管理 - 添加文章相关路由
        (f"{v1_prefix}/articles", ArticleHandler),
        (f"{v1_prefix}/articles/([^/]+)", ArticleHandler),
        (f"{v1_prefix}/articles/([^/]+)/like", ArticleLikeHandler),
        (f"{v1_prefix}/articles/([^/]+)/share", ArticleShareHandler),
        (f"{v1_prefix}/articles/([^/]+)/comments", ArticleCommentCreateHandler),
        (f"{v1_prefix}/articles/([^/]+)/comments/tree", ArticleCommentTreeHandler),
        (f"{v1_prefix}/articles/([^/]+)/comments/search", ArticleCommentSearchHandler),
        (f"{v1_prefix}/articles/comments/([^/]+)", ArticleCommentDeleteHandler),
        (f"{v1_prefix}/articles/comments/audit", ArticleCommentAuditHandler),

        # 新增微信文章相关路由
        (f"{v1_prefix}/wechat/articles", WechatArticlesHandler),
        (f"{v1_prefix}/wechat/articles/([^/]+)/detail", WechatArticleDetailHandler),
        (f"{v1_prefix}/wechat/articles/stats", WechatArticlesStatsHandler),

        #获取评论树
        (f"{v1_prefix}/articles/comment/tree", ArticleCommentTreeHandler),
        (f"{v1_prefix}/articles/comment/create", ArticleCommentCreateHandler),
        (f"{v1_prefix}/articles/comment/audit", ArticleCommentAuditHandler),
        (f"{v1_prefix}/articles/comment/search", ArticleCommentSearchHandler),

        (f"{v1_prefix}/article/collect", ArticleCollectHandler),
        (f"{v1_prefix}/article/collect/check", ArticleCollectCheckHandler),


        (f"{v1_prefix}/articles/comment/delete", ArticleCommentDeleteHandler),



        # 通用搜索路由
        (f"{v1_prefix}/universal-search", UniversalSearchHandler),
        (f"{v1_prefix}/universal-search/articles", ArticleSearchHandler),
        (f"{v1_prefix}/universal-search/projects", ProjectSearchHandler),
        (f"{v1_prefix}/universal-search/stats", SearchStatsHandler),

        # AI对话服务路由
        (f"{v1_prefix}/ai-chat/chat", ChatHandler),
        (r"/ws/ai-chat/stream", ChatStreamHandler),

        # 会话管理路由
        (f"{v1_prefix}/ai-chat/sessions", SessionHandler),
        (f"{v1_prefix}/ai-chat/sessions/list", SessionListHandler),
        (f"{v1_prefix}/ai-chat/sessions/([^/]+)", SessionDetailHandler),
        (f"{v1_prefix}/ai-chat/sessions/([^/]+)/clear", SessionClearHandler),

        # 提示模板路由
        (f"{v1_prefix}/ai-chat/prompt-templates", PromptTemplateCollectionHandler),
        (f"{v1_prefix}/ai-chat/prompt-templates/([^/]+)", PromptTemplateItemHandler),
        (f"{v1_prefix}/ai-chat/prompt-templates/([^/]+)/favorite", PromptTemplateFavoriteHandler),
        (f"{v1_prefix}/ai-chat/prompt-templates/([^/]+)/clone", PromptTemplateCloneHandler),
        (f"{v1_prefix}/ai-chat/prompt-templates/([^/]+)/render", PromptTemplateRenderHandler),

        # 引用管理路由
        (f"{v1_prefix}/references", ReferenceCollectionHandler),
        (f"{v1_prefix}/references/batch", ReferenceBatchHandler),
        (f"{v1_prefix}/references/([^/]+)", ReferenceItemHandler),

        # 热点数据路由
        (f"{v1_prefix}/hot-topics", HotTopicsQueryHandler),            # GET 查询热点
        (f"{v1_prefix}/hot-topics/fetch-store", HotTopicsFetchStoreHandler),  # POST 拉取并存储
        # TopHub 直通接口（原始数据）
        (f"{v1_prefix}/hot-topics/nodes", NodesPageHandler),           # GET p
        (f"{v1_prefix}/hot-topics/nodes/all", NodesAllHandler),        # GET all pages
        (f"{v1_prefix}/hot-topics/node/latest", NodeLatestHandler),    # GET hashid
        (f"{v1_prefix}/hot-topics/node/history", NodeHistoryHandler),  # GET hashid & date
        (f"{v1_prefix}/hot-topics/search", SearchHandler),             # GET q & hashid & p
        (f"{v1_prefix}/hot-topics/hot", HotBoardHandler),              # GET date(optional)

        # 模型管理路由
        (f"{v1_prefix}/ai-chat/models", ModelListHandler),
        (f"{v1_prefix}/ai-chat/models/([^/]+)", ModelConfigHandler),
        (f"{v1_prefix}/ai-chat/providers", ModelProviderHandler),

        # 工具管理路由
        (f"{v1_prefix}/ai-chat/tools", ToolsListHandler),
        (f"{v1_prefix}/ai-chat/tools/config", ToolsConfigHandler),
        (f"{v1_prefix}/ai-chat/tools/([^/]+)/call", ToolCallHandler),
        (f"{v1_prefix}/ai-chat/mcp-servers", MCPServerHandler),

        # artile show
        (f"{v1_prefix}/article/proxy-show", ProxyShowHandler),

        # @gugu相关路由
        (f"{v1_prefix}/articles/guguda/gugu-analysis", GuguAnalysisHandler),  # 接口1：接收AI分析指令
        (f"{v1_prefix}/articles/guguda/gugu-reply", GuguReplyHandler),        # 接口2：发布AI回复评论


        # 逻辑树相关路由
        (f"{v1_prefix}/logictree/current-user-rewritelist", LogicTreeRewriteListHandler),# 接口1 获取用户操纵过的逻辑树列表（入参分页）
        (f"{v1_prefix}/logictree/project-cards-tree", LogicTreeProjectCardsHandler),  # 接口2 获取当前项目的逻辑树（入参是项目id）
        (f"{v1_prefix}/logictree/model-list", LogicTreeModelListHandler),  # 接口3：获取可用ai模型（入参是项目id）V
        (f"{v1_prefix}/logictree/rewrite", LogicTreeRewriteHandler),  # 接口4：重写接口(最快实现，不用ws，入参是提示词，ai模型，cards_id)V
        # (f"{v1_prefix}/logictree/ws", ws),  # 接口5 蹦字websocket
        (f"{v1_prefix}/logictree/trending", LogicTreeTrendingHandler),  # 接口6：获取trendingV
        (f"{v1_prefix}/logictree/associated-project", LogicTreeAssociatedProjectsHandler),  # 接口7：获取关联项目V
    ]

    upload_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'images')
    # 添加静态文件处理路由
    static_routes = [
        # 使用StaticFileHandler处理图片文件
        (r"/static/images/(.*)", CORSStaticFileHandler, {"path": upload_dir}),
    ]

    # 智能文章生成路由
    intelligent_article_routes = get_intelligent_article_routes()

    # 未来可以添加其他版本的API路由
    routes = [
        *v1_routes,
        *intelligent_article_routes,
    ]

    routes.extend(static_routes)

    # add 3/2
    urlspecs = []
    for route in routes:
        if len(route) == 2:
            pattern, handler = route
            urlspecs.append(URLSpec(pattern, handler))
        elif len(route) == 3:
            pattern, handler, kwargs = route
            urlspecs.append(URLSpec(pattern, handler, kwargs=kwargs))

    return urlspecs

    # 转换为URLSpec
    # return [URLSpec(pattern, handler) for pattern, handler in routes]
