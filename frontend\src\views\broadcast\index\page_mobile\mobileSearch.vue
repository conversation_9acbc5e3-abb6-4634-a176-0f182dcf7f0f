<template>
  <div class="app-container home">
    <RollingSearch
      :showAddProject="false"
      v-model:searchKey="searchKeyMobile"
      @search="doSearch(searchKeyMobile)"></RollingSearch>
    <div
      class="tab-wrap"
      v-if="searchKeyUsing && !(!currentTab && !list2?.length > 0)">
      <div
        class="tab"
        v-for="item in tabItems"
        :key="item.key"
        :class="{ active: currentTab === item.key }"
        @click="changeTab(item)">
        {{ item.label }}
      </div>
    </div>
    <div class="content" ref="homeRef">
      <template v-if="searchKeyUsing">
        <WaterfallLayout
          class="waterfall"
          :options="options2"
          :list="list2"
          :isOver="isOver"
          :isLoading="isLoading"
          :scroll-dom="homeRef"
          @getNext="getNext">
          <template v-slot:default="slotProp">
            <div
              class="list-item"
              v-preventLongPress="() => toDetails(slotProp.item, 'search')">
              <div
                v-if="getHighlightName(slotProp.item)?.length > 0"
                style="display: flex; align-items: center; margin-bottom: 12px">
                <img
                  v-if="slotProp.item.content_type === 'article'"
                  class="markicon"
                  src="@/assets/images/icon30.png"
                  alt="" />
                <div
                  class="list-item-title"
                  v-html="getHighlightName(slotProp.item)"></div>
              </div>
              <div
                v-else
                class="list-item-title"
                style="display: flex; align-items: center; margin-bottom: 12px">
                <img
                  v-if="slotProp.item.content_type === 'article'"
                  class="markicon"
                  src="@/assets/images/icon30.png"
                  alt="" />
                {{ slotProp.item.title }}
              </div>
              <!-- <div
              class="list-item-content"
              v-if="slotProp.item.highlights"
              v-for="item in slotProp.item.highlights?.highlights"
              v-html="cleanSpecialChars(item.fragments?.[0])"></div> -->
              <div
                class="list-item-content description_project"
                v-html="getHighlightDescription(slotProp.item)"></div>
              <div
                class="list-item-content"
                v-if="slotProp.item.matching_cards"
                v-for="card in slotProp.item.matching_cards">
                <div
                  v-for="item in card.highlights?.highlights"
                  v-html="cleanSpecialChars(item.fragments?.[0])"></div>
              </div>
            </div>
          </template>
          <template v-slot:footer>
            <div class="own-over-text">
              {{ overText }}
              <div
                class="more-wrap"
                v-show="overText.includes('未找到相关的资讯或仓库')">
                您可以试试搜索
                <span @click="toNewindex('AI')" class="more">AI</span>
                、
                <span @click="toNewindex('平台')" class="more">平台</span>
                、
                <span @click="toNewindex('数据库')" class="more">数据库</span>
                ...
              </div>
            </div>
            <div
              class="over-text-btns"
              v-show="overText.includes('正在努力分析中')">
              <div class="over-text-btn" @click="searchKeyMobile = ''">
                取消
              </div>
              <div class="over-text-btn color" @click="handleFenxi">确认</div>
            </div>
          </template>
        </WaterfallLayout>
      </template>
      <template v-else>
        <div class="discover" v-if="allSuggestions.length > 0">
          <div class="title">
            <div>历史搜索</div>
            <el-icon @click="handleDelete"><Delete /></el-icon>
          </div>
          <div class="item-list">
            <div
              class="item"
              v-for="item in allSuggestions"
              @click="handleSelect(item)">
              {{ item }}
            </div>
          </div>
        </div>
        <div class="discover">
          <div class="title">发现</div>
          <div class="item-list">
            <div
              class="item"
              v-for="item in discoverList"
              @click="handleSelect(item)">
              {{ item }}
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup name="BroadcastMobileSearch">
import useUserStore from "@/store/modules/user";
import { onMounted, ref } from "vue";
import { isHttp } from "@/utils/validate";
import { validateRepo, normaldownload, searchEs } from "@/api/broadcast.js";
import { getArticleList } from "@/api/article.js";
import WaterfallLayout from "@/components/WaterfallLayout/index.vue";
import RollingSearch from "@/views/broadcast/components/RollingSearch.vue";
import { useDebounceFn } from "@vueuse/core";
import {
  getHighlightName,
  getMarkedTextFromItem,
  cleanSpecialChars,
  getHighlightDescription,
} from "@/utils/articleSolve";
const { proxy } = getCurrentInstance();
const userStore = useUserStore();
const router = useRouter();
const route = useRoute();
let tabItems = ref([
  { key: "project", label: "仓库", count: 0 },
  { key: "article", label: "资讯", count: 0 },
]);
let currentTab = ref("");

let searchKeyMobile = ref(route.query.searchKeyMobile || "");
let discoverList = ref([
  "动画库",
  "提示词",
  "编程助手",
  "聊天机器人",
  "网页工具",
  "数据处理",
]);
let allSuggestions = ref([]);

let list2 = ref([]);
let list3 = ref([]);
let homeRef = ref(null);
let options2 = ref({
  gutter: 10,
  hasAroundGutter: false,
  space: 10,
  breakpoints: {
    960: {
      rowPerView: 1,
    },
  },
});

//控制请求数据时显示加载状态提示
let isLoading = ref(false);
//控制数据是否已经全部加载完成(即不需要再滚动加载)
let isOver = ref(false);
let overText = ref("呀，被看光了！");
//正在查询中的searchKey,有值显示列表,否则显示卡片
let searchKeyUsing = ref("");
let searching = ref(false);
let page_size = ref(10);
let page = ref(1);
onMounted(() => {
  allSuggestions.value =
    JSON.parse(localStorage.getItem("searchHistory")) || [];
});

const doSearch = (search, type) => {
  searchKeyMobile.value = search?.trim();
  searchKeyUsing.value = searchKeyMobile.value;
  if (!searchKeyMobile.value) {
    proxy.$modal.msgError("请输入搜索关键词");
    return;
  }
  if (type !== "getNext") {
    page.value = 1;
    page_size.value = 10;
    isOver.value = false;
    list2.value = [];
    list3.value = [];
    setTimeout(() => {
      //增加定时器，修复Safari上直接scrollTop不生效的问题
      document.querySelector(".content").scrollTop = 0;
    }, 10);
  }

  // 每次搜索之前判断输入词是否是http url，如果是，则判断是否是合法的github地址以及是否被本地收录
  // 如果被本地收录了，直接显示返回的项目信息，否则显示提示文字
  if (isHttp(searchKeyMobile.value?.split(" ")?.[0])) {
    if (searching.value) {
      return;
    }
    searching.value = true;
    isLoading.value = true;
    validateRepo({ url: searchKeyMobile.value })
      .then((res) => {
        if (res.code == 200) {
          list2.value = [];
          isOver.value = true;
          if (res.data.exists) {
            // 被本地收录了，直接显示返回的项目信息
            list2.value = res.data.project ? [res.data.project] : [];
            overText.value = "";
          } else if (res.data.valid) {
            // 未被收录，显示提示文字
            overText.value = "gugu正在努力分析中，有结果第一时间同步给您。";
          } else {
            overText.value = "未找到与链接匹配的仓库！";
          }
        }
      })
      .catch((err) => {
        overText.value = "";
        isOver.value = true;
      })
      .finally(() => {
        isLoading.value = false;
        searching.value = false;
      });
  } else {
    if (searchKeyUsing.value) {
      getSearchData();
    } else {
      // getData();
    }
  }
};
onMounted(() => {
  if (searchKeyMobile.value) {
    doSearch(searchKeyMobile.value);
  }
});
watch(searchKeyMobile, (value) => {
  currentTab.value = "";
  if (value === "") {
    searchKeyUsing.value = "";
  }
});
const handleSelect = (item) => {
  doSearch(item);
};

const handleDelete = () => {
  proxy.$modal.confirm("确认删除全部历史搜索记录？").then(() => {
    allSuggestions.value = [];
    localStorage.removeItem("searchHistory");
  });
};
const getNext = useDebounceFn(() => {
  // console.log("getNext", isOver.value);
  doSearch(searchKeyUsing.value, "getNext");
}, 1000);
const getSearchData = () => {
  isLoading.value = true;
  searching.value = true;
  searchEs({
    content_types: currentTab.value || "all",
    query: searchKeyUsing.value,
    filters: {
      status: "published",
    },
    page: page.value,
    size: page_size.value,
    include_cards: true,
    highlight: true,
  })
    .then((res) => {
      if (res.code == 200) {
        list2.value = list2.value.concat(res.data?.items || []);
        tabItems.value[0].count = res.data?.total_projects;
        tabItems.value[1].count = res.data?.total_articles;
        if (list2.value.length >= res.data?.total) {
          isOver.value = true;
          overText.value = "呀，被看光了！";
        } else {
          page.value = page.value + 1;
        }
        if (!(list2.value?.length > 0)) {
          overText.value = "未找到与关键词匹配的仓库！";
        } else if (list2.value?.length <= 10) {
          overText.value = "";
        }
      }
    })
    .catch((err) => {
      overText.value = "";
      isOver.value = true;
    })
    .finally(() => {
      isLoading.value = false;
      searching.value = false;
    });
};
let projectReadList = ref(
  JSON.parse(localStorage.getItem("projectReadList")) || []
);
const changeTab = (item) => {
  if (currentTab.value === item.key) {
    currentTab.value = "";
  } else {
    currentTab.value = item.key;
  }
  doSearch(searchKeyUsing.value);
};
// 将项目id加入已读列表。并存在localStorage里面
const addReadList = (item) => {
  let readList = JSON.parse(localStorage.getItem("projectReadList")) || [];
  if (!readList.includes(item.id)) {
    readList.unshift(item.id);
  }
  readList = readList.slice(0, 100); //最多100个
  localStorage.setItem("projectReadList", JSON.stringify(readList));
  // 手动更新响应式数据
  projectReadList.value = readList;

  //如果未登录，还要同时保存最近读的一个项目的tag到sessionstorage
  if (!userStore.name) {
    sessionStorage.setItem("latestTags", item.tags?.join(",") || "");
  }
};
const toDetails = (item, type) => {
  setTimeout(() => {
    addReadList(item);
  }, 2000);
  const path =
    item.content_type === "project"
      ? "/broadcast/details"
      : "/broadcast/article";
  // 如果type是search还要带上参数：searchKey、匹配的卡片id
  let query = {
    id: item.id,
  };
  if (type === "search") {
    if (!isHttp(searchKeyMobile.value?.trim()?.split(" ")?.[0])) {
      query.search = getMarkedTextFromItem(item);
      query.cardIds = item.matching_cards
        ?.map((e) => {
          return e.id;
        })
        ?.join(",");
    }
    // 构建URL和查询参数
    const queryString = Object.entries(query)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join("&");
    // 在新窗口打开
    window.open(`${path}?${queryString}`, "_blank");
    return;
  }
  router.push({
    path: path,
    query: query,
  });
};
const toNewindex = (key) => {
  doSearch(key);
};
const handleFenxi = () => {
  if (!userStore.userInfo.username) {
    router.push("/mobileLogin");
  } else {
    let params = {
      projects: [{ repository_url: searchKeyUsing.value }],
    };
    proxy.$modal.loading();
    normaldownload(params)
      .then(() => {
        proxy.$modal.msgSuccess("操作成功");
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }
};
</script>

<style scoped lang="scss">
.app-container {
  background:
    radial-gradient(at 20% 0%, rgba(202, 229, 245, 0.6), transparent 20%),
    radial-gradient(at 60% -15%, rgba(202, 229, 245, 0.7), transparent 40%),
    radial-gradient(at 100% 100%, rgba(160, 235, 248, 0.7), transparent 40%),
    radial-gradient(at 0% 100%, rgba(243, 252, 202, 1), transparent 30%);
  padding: 0;
}
.home {
  position: relative;
  height: calc(100vh - var(--navbar-height));
  overflow-y: hidden;
  scroll-behavior: smooth;
  width: 100vw;
  .discover {
    width: 100%;
    padding: 10px 18px;
    .title {
      font-size: 14px;
      color: #4a4a4a;
      font-weight: bold;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .item-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      .item {
        padding: 4px 10px;
        border-radius: 8px;
        background-color: #f4f4f4;
        color: #989898;
        font-size: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
        &:hover {
          background-color: #d5edf0;
        }
      }
    }
  }
  .content {
    height: calc(100% - 80px - 40px);
    overflow-y: auto;
    scroll-behavior: smooth;
  }
}
.waterfall {
  padding: 6px 10px;
}
.tab-wrap {
  display: flex;
  align-items: center;
  font-size: 12px;
  width: 100%;
  height: 30px;
  padding: 0 20px;
  margin: 10px auto 0;
  .tab {
    border-radius: 4px;
    border: solid 1px #55c4d5;
    cursor: pointer;
    padding: 4px 16px;
    &.active {
      background-color: #55c4d5;
    }
    & + .tab {
      margin-left: 16px;
    }
  }
}
.own-over-text {
  font-size: 14px;
  color: #999;
  text-align: center;
  margin-top: 60px;
  .more-wrap {
    margin-top: 10px;
  }
  .more {
    color: #0048bd;
    cursor: pointer;
  }
}
.over-text-btns {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  .over-text-btn {
    border: solid 1px #55c4d5;
    cursor: pointer;
    border-radius: 12px;
    width: 120px;
    height: 28px;
    font-size: 12px;
    color: #55c4d5;
    line-height: 28px;
    text-align: center;
    & + .over-text-btn {
      margin-left: 26px;
    }
    &.color {
      background-color: #55c4d5;
      color: #fff;
    }
  }
}

.list-item {
  background-color: #f0f0f0;
  border-radius: 4px;
  padding: 10px 20px;
  cursor: pointer;
  color: #111;
  &:hover {
    background-color: #d5edf0;
  }
  .list-item-title {
    font-size: 14px;
    font-weight: bold;
    color: #000;
  }
  .list-item-content {
    font-size: 12px;
    color: #232323;
    word-break: break-all;
    line-height: 18px;
    text-wrap: pretty;
    &.description_project {
      color: #6f6f6f;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      line-clamp: 3;
      -webkit-line-clamp: 3; /* 限制显示的行数 */
      overflow: hidden;
      text-overflow: ellipsis;
    }
    & + .list-item-content {
      margin-top: 6px;
    }
  }
}
.markicon {
  width: 24px;
  height: 24px;
  margin-right: 4px;
}
:deep(.el-input__wrapper) {
  box-shadow: none;
  border: none;
  font-size: 14px;
  background-color: transparent;
  padding-top: 2px;
}
:deep(.el-input-group__append) {
  background-color: transparent;
  border: none;
  padding-left: 10px;
  box-shadow: none;
}
</style>
