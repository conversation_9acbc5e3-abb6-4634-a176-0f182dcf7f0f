import { createWebHistory, createRouter } from "vue-router";
/* Layout */
import Layout from "@/layout";
import LayoutBroadcast from "@/layout/broadcast";

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: "/",
    redirect: "/broadcast/index",
    hidden: true,
  },
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index.vue"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login"),
    hidden: true,
  },
  {
    path: "/register",
    component: () => import("@/views/register"),
    hidden: true,
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import("@/views/error/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error/401"),
    hidden: true,
  },
  {
    path: "/index",
    component: Layout,
    children: [
      {
        path: "",
        component: () => import("@/views/index"),
        name: "Index",
        meta: { title: "首页", icon: "dashboard", affix: true },
      },
    ],
  },
  {
    path: "/mobileLogin",
    component: () => import("@/layout/components/Login/index"),
    hidden: true,
    meta: {
      noCache: false,
      link: null,
    },
  },
  {
    path: "/mobileRegister",
    component: () => import("@/layout/components/Register/index"),
    hidden: true,
    meta: {
      noCache: false,
      link: null,
    },
  },
  {
    path: "/mobileEmail",
    component: () => import("@/layout/components/Email/index"),
    hidden: true,
    meta: {
      noCache: false,
      link: null,
    },
  },
  {
    path: "/broadcast", // 明确定义父路径
    component: LayoutBroadcast,
    meta: {
      noCache: false,
      link: null,
      title: "gugu播报",
    },
    hidden: true,
    redirect: "/broadcast/index",
    children: [
      {
        name: "BroadcastIndex",
        path: "index", // 使用相对路径
        hidden: true,
        component: () => import("@/views/broadcast/index/index"),
      },
      {
        name: "BroadcastMobileSearch",
        path: "mobileSearch", // 使用相对路径
        hidden: true,
        component: () =>
          import("@/views/broadcast/index/page_mobile/mobileSearch"),
      },
      {
        name: "BroadcastDetails",
        path: "details", // 使用相对路径
        hidden: true,
        component: () => import("@/views/broadcast/details/index"),
      },
      {
        name: "BroadcastArticle",
        path: "article", // 使用相对路径
        hidden: true,
        component: () => import("@/views/broadcast/article/index"),
      },
    ],
  },

  {
    path: "/test",
    component: Layout,
    meta: {
      title: "测试",
      icon: "bug",
      noCache: false,
      link: null,
    },
    redirect: "noredirect",
    children: [
      {
        name: "Index1",
        path: "index1",
        hidden: false,
        component: () => import("@/views/test/index1"),
        meta: {
          title: "测试1",
          icon: "bug",
          noCache: false,
          link: null,
        },
      },
      {
        name: "Index2",
        path: "index2",
        hidden: false,
        component: () => import("@/views/test/index2"),
        meta: {
          title: "测试2",
          icon: "bug",
          noCache: false,
          link: null,
        },
      },
      {
        name: "Index3",
        path: "index3",
        hidden: false,
        component: () => import("@/views/test/index3"),
        meta: {
          title: "测试3",
          icon: "bug",
          noCache: false,
          link: null,
        },
      },
      {
        name: "Index4",
        path: "index4",
        hidden: false,
        component: () => import("@/views/test/index4"),
        meta: {
          title: "测试4",
          icon: "bug",
          noCache: false,
          link: null,
        },
      },
      {
        name: "Index5",
        path: "index5",
        hidden: false,
        component: () => import("@/views/test/index5"),
        meta: {
          title: "测试5",
          icon: "bug",
          noCache: false,
          link: null,
        },
      },
    ],
  },
  {
    path: "/legal/userAgreement",
    component: () => import("@/views/legal/userAgreement"),
    meta: { title: "用户条款" },
    hidden: true,
  },
  {
    path: "/legal/privacyPolicy",
    component: () => import("@/views/legal/privacyPolicy"),
    meta: { title: "隐私协议" },
    hidden: true,
  },
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: "/system/user-auth",
    component: Layout,
    hidden: true,
  },
  {
    path: "/system/dict-data",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "index/:dictId(\\d+)",
        component: () => import("@/views/system/dict/data"),
        name: "Data",
        meta: { title: "字典数据", activeMenu: "/system/dict" },
      },
    ],
  },
  {
    path: "/personal",
    component: LayoutBroadcast,
    meta: {
      noCache: false,
      link: null,
    },
    hidden: true,
    roles: ["user"],
    redirect: "/personal/homepage",
    children: [
      {
        name: "BroadcastPersonalChangePassword",
        path: "changePassword",
        hidden: true,
        component: () =>
          import("@/views/broadcast/personal/homepage/page_mobile/Edit"),
        meta: {
          noCache: false,
          title: "编辑",
        },
      },
      {
        name: "BroadcastPersonalHomepage",
        path: "homepage",
        hidden: true,
        component: () => import("@/views/broadcast/personal/homepage/index"),
        meta: {
          noCache: false,
          title: "个人资料",
        },
      },

      {
        name: "BroadcastPersonalTasklist",
        path: "tasklist",
        hidden: true,
        component: () => import("@/views/broadcast/personal/tasklist/index"),
        meta: {
          noCache: false,
          title: "任务列表",
        },
      },
      {
        name: "BroadcastPersonalHistoryrecord",
        path: "historyrecord",
        hidden: true,
        component: () =>
          import("@/views/broadcast/personal/historyrecord/index"),
        meta: {
          noCache: false,
          title: "历史记录",
        },
      },
      {
        name: "BroadcastPersonalFavorite",
        path: "favorite",
        hidden: true,
        component: () => import("@/views/broadcast/personal/favorite/index"),
        meta: {
          noCache: false,
          title: "收藏夹",
        },
      },
      {
        name: "BroadcastPersonalMessage",
        path: "message",
        hidden: true,
        component: () => import("@/views/broadcast/personal/message/index"),
        meta: {
          noCache: false,
          title: "消息中心",
        },
      },
      {
        name: "BroadcastPersonalAddProject",
        path: "addProject",
        hidden: true,
        component: () =>
          import("@/views/broadcast/index/page_mobile/addProject"),
        meta: {
          noCache: false,
          title: "添加项目分析",
        },
      },
    ],
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    }
    return { top: 0 };
  },
});

export default router;
