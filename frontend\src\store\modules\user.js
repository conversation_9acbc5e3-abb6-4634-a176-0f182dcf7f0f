import { login, logout, getInfo } from "@/api/login";
import { getToken, setToken, removeToken } from "@/utils/auth";
import { isHttp, isEmpty } from "@/utils/validate";
import defAva from "@/assets/images/profile.png";

const useUserStore = defineStore("user", {
  state: () => ({
    token: getToken(),
    id: "",
    name: "",
    avatar: "",
    roles: [],
    permissions: [],
    userInfo: {},
    loginDialogVisible: false,
    unread_count: 0, // 未读数
    replyName: "", // 显示哪条回复的评论回复框
    showCommentInput: false, // 显示文章下面的回复框
    refreshComment: null, //评论更新的时候刷新评论
    allMsgStatus: {}, // 所有评论的状态
  }),
  actions: {
    // 登录
    login(userInfo) {
      const username = userInfo.username.trim();
      const password = userInfo.password;
      const code = userInfo.code;
      const uuid = userInfo.uuid;
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid)
          .then((res) => {
            setToken(res?.data?.token?.access_token);
            this.token = res?.data?.token?.access_token;
            localStorage.setItem("userId", res?.data?.token?.user_id);
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 获取用户信息
    getInfo() {
      return new Promise((resolve, reject) => {
        let userId = localStorage.getItem("userId");
        getInfo(userId)
          .then((res) => {
            const user = res.data;
            let avatar = user.avatar || "";
            if (!isHttp(avatar)) {
              avatar = isEmpty(avatar)
                ? defAva
                : import.meta.env.VITE_APP_BASE_API + avatar;
            }
            if (user.roles && user.roles.length > 0) {
              this.roles = user.roles;
              this.permissions = user.permissions;
            } else {
              this.roles = ["DEFAULT"];
              // this.roles = ["admin"];
              this.permissions = [];
            }
            // console.log("this.roles", this.roles);

            this.id = user.id;
            this.name = user.username;
            this.avatar = avatar;
            this.userInfo = user;
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 退出系统
    logOut() {
      return new Promise((resolve, reject) => {
        logout(this.token)
          .then(() => {
            resolve();
          })
          .catch((error) => {
            reject(error);
          })
          .finally(() => {
            this.token = "";
            this.id = "";
            this.name = "";
            this.avatar = "";
            this.userInfo = {};
            this.roles = [];
            this.permissions = [];
            localStorage.removeItem("userId");
            removeToken();
          });
      });
    },
  },
});

export default useUserStore;
