import structlog
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy import select, and_, func, desc, or_
from sqlalchemy.orm import selectinload
from app.core.di.providers import AsyncSessionProvider
from app.models.github import GitHubProjectModel
from app.models.github.github_project_card import GitHubProjectCardModel  # 使用现有的卡片模型
from app.models.github.github_project_logictree import LogicTreeRewriteModel
from datetime import datetime, timezone, timedelta
from app.schemas.github.github_project_logictree import (
    LogicTreeCardCreate, LogicTreeCard, LogicTreeRewriteCreate,
    LogicTreeRewrite, LogicTreeCardTree, LogicTreeRewriteListResponse,
    LogicTreeModelInfo, LogicTreeCardTreeNew
)
from app.schemas.github.github_trending import GitHubTrendingResponse, GitHubTrendingProject
from app.utils.security import get_current_user_id
from app.services.github.github_readme_generater import GitHubReadmeGenerate
import asyncio
import aiohttp

from app.utils.status_enum import ProjectStatusEnum
from pydantic import BaseModel, Field

logger = structlog.get_logger(__name__)


class LogicTreeService:
    """逻辑树服务"""
    
    # 类级别的缓存变量，所有实例共享
    _daily_trending_cache = {}
    _cache_date = None
    _cache_lock = asyncio.Lock()  # 防止并发请求时重复获取API数据

    def __init__(self, async_session: AsyncSessionProvider):
        self.async_session = async_session

    async def get_similar_list(
            self,
            project_id: str,
            size: int = 10
    ) -> LogicTreeRewriteListResponse:
        """获取相似项目列表"""
        try:
            # 生成假的相似项目数据
            fake_similar_projects = []

            # 模拟项目描述模板
            project_descriptions = [
                "一个现代化的Web应用框架，提供高性能和易用性",
                "基于AI的智能代码分析工具，帮助开发者提升代码质量",
                "轻量级数据库ORM库，支持多种数据库类型",
                "实时数据可视化平台，支持多种图表类型",
                "微服务架构管理系统，提供完整的服务治理能力",
                "机器学习模型训练平台，支持多种算法",
                "分布式缓存解决方案，提供高性能数据存储",
                "API网关服务，统一管理微服务接口",
                "容器编排平台，简化应用部署和管理",
                "消息队列中间件，支持高并发消息处理",
                "前端UI组件库，提供丰富的交互组件",
                "后端API框架，快速构建RESTful服务",
                "数据ETL工具，支持多种数据源转换",
                "监控告警系统，实时监控应用性能",
                "用户权限管理系统，细粒度权限控制"
            ]

            # 生成指定数量的假数据
            for i in range(min(size, len(project_descriptions))):
                fake_project = {
                    "id": f"project_{project_id}_{i + 1}",  # 生成唯一的项目ID
                    "description": project_descriptions[i],  # 使用预定义的描述
                    "name": f"相似项目 {i + 1}",  # 项目名称
                    "stars": f"{1000 + i * 100}",  # 模拟星标数
                    "created_at": "2024-01-01T00:00:00Z",  # 模拟创建时间
                    "updated_at": "2024-01-01T00:00:00Z"  # 模拟更新时间
                }
                fake_similar_projects.append(fake_project)

            # 返回响应格式
            return LogicTreeRewriteListResponse(
                items=fake_similar_projects,
                total=len(fake_similar_projects),
                page=1,
                page_size=size
            )

        except Exception as e:
            logger.error("获取相似列表失败", error=str(e), project_id=project_id)
            raise ValueError(f"获取相似列表失败: {str(e)}")

    async def get_user_rewrite_list(
        self, 
        user_id: str, 
        page: int = 1, 
        page_size: int = 10
    ) -> LogicTreeRewriteListResponse:
        """获取用户重写列表"""
        try:
            async with self.async_session() as session:
                # 构建查询
                query = select(LogicTreeRewriteModel).where(
                    LogicTreeRewriteModel.user_id == user_id
                ).order_by(desc(LogicTreeRewriteModel.created_at))
                
                # 获取总数
                count_query = select(func.count(LogicTreeRewriteModel.id)).where(
                    LogicTreeRewriteModel.user_id == user_id
                )
                total = await session.scalar(count_query)
                
                # 分页
                offset = (page - 1) * page_size
                query = query.offset(offset).limit(page_size)
                
                # 执行查询
                result = await session.execute(query)
                rewrites = result.scalars().all()
                
                # 构建响应数据
                items = []
                for rewrite in rewrites:
                    item = {
                        "id": rewrite.id,
                        "card_id": rewrite.card_id,
                        "project_id": rewrite.project_id,
                        "prompt": rewrite.prompt,
                        "model_name": rewrite.model_name,
                        "status": rewrite.status,
                        "created_at": rewrite.created_at,
                        "updated_at": rewrite.updated_at
                    }
                    items.append(item)
                
                return LogicTreeRewriteListResponse(
                    items=items,
                    total=total,
                    page=page,
                    page_size=page_size
                )
                
        except Exception as e:
            logger.error("获取用户重写列表失败", error=str(e), user_id=user_id)
            raise ValueError(f"获取用户重写列表失败: {str(e)}")

    # @GeneratedBy:AI
    async def get_project_cards_tree_new(
            self,
            project_id: str,
            user_id: Optional[str] = None
    ) -> List[LogicTreeCardTreeNew]:
        """获取项目逻辑树新版本 - 分两次查询构建树形结构"""
        try:
            async with self.async_session() as session:
                # 第一次查询：只查询默认卡片（created_by为None）
                default_cards_query = select(GitHubProjectCardModel).where(
                    and_(
                        GitHubProjectCardModel.project_id == project_id,
                        GitHubProjectCardModel.created_by.is_(None)
                    )
                ).order_by(GitHubProjectCardModel.level, GitHubProjectCardModel.created_at)

                result = await session.execute(default_cards_query)
                default_cards = result.scalars().all()

                # 第二次查询：如果有user_id，查询用户自己的卡片
                user_cards = []
                if user_id:
                    user_cards_query = select(GitHubProjectCardModel).where(
                        and_(
                            GitHubProjectCardModel.project_id == project_id,
                            GitHubProjectCardModel.created_by == user_id
                        )
                    ).order_by(GitHubProjectCardModel.level, GitHubProjectCardModel.created_at)

                    result = await session.execute(user_cards_query)
                    user_cards = result.scalars().all()

                # 先构建默认卡片的list
                card_dict = {}
                root_cards = []

                # 处理默认卡片，构建基础树形结构
                for card in default_cards:
                    card_data = LogicTreeCard(
                        id=card.id,
                        project_id=card.project_id,
                        original_card_id=card.original_card_id,
                        title=card.title,
                        content=card.content,
                        level=card.level,
                        parent_card_id=card.parent_card_id,
                        is_original=card.is_original,
                        is_active=card.is_active,
                        rewrite_count=card.rewrite_count,
                        like_count=card.like,
                        collect_count=card.collect,
                        created_at=card.created_at,
                        updated_at=card.updated_at
                    )

                    # 创建树节点，children初始化为None
                    card_tree = LogicTreeCardTreeNew(
                        card=card_data,
                        children=None,
                        history_keywords=[]  # 初始化为空，后续会填充所有历史关键词
                    )
                    card_dict[card.id] = card_tree

                    # 如果是根节点，添加到根列表
                    if not card.parent_card_id:
                        root_cards.append(card_tree)

                # 为默认卡片建立父子关系
                for card in default_cards:
                    if card.parent_card_id and card.parent_card_id in card_dict:
                        parent_node = card_dict[card.parent_card_id]
                        child_node = card_dict[card.id]
                        # children不是list，直接赋值单个子节点
                        parent_node.children = child_node

                # 处理用户卡片，收集所有历史关键词
                user_cards_by_original = {}
                all_history_prompts_by_original = {}  # 收集所有历史关键词

                for card in user_cards:
                    original_id = card.original_card_id or card.id

                    # 收集所有历史关键词（不只是最新的）
                    if original_id not in all_history_prompts_by_original:
                        all_history_prompts_by_original[original_id] = []
                    if card.prompt:
                        all_history_prompts_by_original[original_id].append(card.prompt)

                    # 保留最新的用户重写卡片
                    if (original_id not in user_cards_by_original or 
                        card.created_at > user_cards_by_original[original_id].created_at):
                        user_cards_by_original[original_id] = card

                # 将用户的最新卡片作为对应默认卡片的children
                for original_id, user_card in user_cards_by_original.items():
                    if original_id in card_dict:
                        # 获取所有历史关键词
                        all_history_keywords = all_history_prompts_by_original.get(original_id, [])
                        
                        # 创建用户卡片数据
                        user_card_data = LogicTreeCard(
                            id=user_card.id,
                            project_id=user_card.project_id,
                            original_card_id=user_card.original_card_id,
                            title=user_card.title,
                            content=user_card.content,
                            level=user_card.level,
                            parent_card_id=user_card.parent_card_id,
                            is_original=user_card.is_original,
                            is_active=user_card.is_active,
                            rewrite_count=user_card.rewrite_count,
                            like_count=user_card.like,
                            collect_count=user_card.collect,
                            created_at=user_card.created_at,
                            updated_at=user_card.updated_at
                        )
                        
                        # 创建用户卡片的树节点
                        user_card_tree = LogicTreeCardTreeNew(
                            card=user_card_data,
                            children=None,  # 用户卡片是叶子节点
                            history_keywords=all_history_keywords  # 用户卡片也包含所有历史关键词
                        )
                        
                        # 将用户卡片作为默认卡片的children
                        default_node = card_dict[original_id]
                        default_node.children = user_card_tree
                        # 将所有历史关键词添加到默认卡片上
                        default_node.history_keywords = all_history_keywords
            
                return root_cards
            
        except Exception as e:
            logger.error("获取项目逻辑树新版本失败", error=str(e), project_id=project_id)
            raise ValueError(f"获取项目逻辑树新版本失败: {str(e)}")

    async def get_project_cards_tree(
        self, 
        project_id: str,
        user_id: Optional[str] = None
    ) -> List[LogicTreeCardTree]:
        """获取项目逻辑树 - 使用现有的github_project_cards表"""
        try:
            async with self.async_session() as session:
                # 构建查询条件
                conditions = [GitHubProjectCardModel.project_id == project_id]

                if user_id:
                    conditions.append(
                        or_(
                            GitHubProjectCardModel.created_by == user_id,
                            GitHubProjectCardModel.created_by.is_(None)
                        )
                    )

                # 查询卡片
                query = select(GitHubProjectCardModel).where(
                    and_(*conditions)
                ).order_by(GitHubProjectCardModel.level, GitHubProjectCardModel.created_at)
                
                result = await session.execute(query)
                all_cards = result.scalars().all()

                filtered_cards = []
                user_cards_by_original = {}

                for card in all_cards:
                    if card.created_by is None:
                        # 默认卡片，直接保留
                        filtered_cards.append(card)
                    else:
                        # 用户重写卡片，记录最新的
                        original_id = card.original_card_id or card.id
                        if original_id not in user_cards_by_original or card.created_at > user_cards_by_original[
                            original_id].created_at:
                            user_cards_by_original[original_id] = card

                # 添加最新的用户重写卡片
                filtered_cards.extend(user_cards_by_original.values())

                # 构建树形结构
                card_dict = {}
                root_cards = []
                
                for card in filtered_cards:
                    card_data = LogicTreeCard(
                        id=card.id,
                        project_id=card.project_id,
                        # father_project_id=card.father_project_id,
                        original_card_id=card.original_card_id,
                        title=card.title,
                        content=card.content,
                        level=card.level,
                        parent_card_id=card.parent_card_id,
                        is_original=card.is_original,
                        is_active=card.is_active,
                        rewrite_count=card.rewrite_count,
                        like_count=card.like,
                        collect_count=card.collect,
                        created_at=card.created_at,
                        updated_at=card.updated_at
                    )
                    
                    card_tree = LogicTreeCardTree(card=card_data, children=[])
                    card_dict[card.id] = card_tree
                    
                    if card.parent_card_id and card.parent_card_id in card_dict:
                        card_dict[card.parent_card_id].children.append(card_tree)
                    else:
                        root_cards.append(card_tree)
                
                return root_cards
                
        except Exception as e:
            logger.error("获取项目逻辑树失败", error=str(e), project_id=project_id)
            raise ValueError(f"获取项目逻辑树失败: {str(e)}")

    async def get_available_models(self, project_id: str) -> List[LogicTreeModelInfo]:
        """获取可用AI模型列表"""
        try:
            # 这里应该从配置或数据库获取可用模型
            # 暂时返回硬编码的模型列表
            models = [
                LogicTreeModelInfo(
                    name="gpt-4",
                    display_name="GPT-4",
                    description="OpenAI GPT-4 模型",
                    is_available=True
                ),
                LogicTreeModelInfo(
                    name="gpt-3.5-turbo",
                    display_name="GPT-3.5 Turbo",
                    description="OpenAI GPT-3.5 Turbo 模型",
                    is_available=True
                ),
                LogicTreeModelInfo(
                    name="claude-3-sonnet",
                    display_name="Claude 3 Sonnet",
                    description="Anthropic Claude 3 Sonnet 模型",
                    is_available=True
                ),
                LogicTreeModelInfo(
                    name="claude-3-haiku",
                    display_name="Claude 3 Haiku",
                    description="Anthropic Claude 3 Haiku 模型",
                    is_available=True
                )
            ]
            
            return models
            
        except Exception as e:
            logger.error("获取可用模型列表失败", error=str(e), project_id=project_id)
            raise ValueError(f"获取可用模型列表失败: {str(e)}")

    async def rewrite_card(
        self, 
        card_id: str, 
        project_id: str, 
        prompt: str, 
        model_name: str,
        user_id: str,
        rewrite_text: str
    ) -> LogicTreeCard:
        """重写卡片 - 在github_project_cards中创建新卡片"""
        try:
            async with self.async_session() as session:
                # 获取原始卡片
                original_card_query = select(GitHubProjectCardModel).where(
                    GitHubProjectCardModel.id == card_id
                )
                result = await session.execute(original_card_query)
                original_card = result.scalar_one_or_none()
                
                if not original_card:
                    raise ValueError(f"卡片不存在: {card_id}")
                
                # 这里应该调用AI服务进行重写
                # 暂时返回模拟的重写内容
                rewritten_content = f" {original_card.content}\n\n {prompt} \n\n {rewrite_text}"
                
                # 在github_project_cards中创建新的重写卡片
                new_card = GitHubProjectCardModel(
                    project_id=project_id,
                    # father_project_id=original_card.father_project_id,
                    original_card_id=original_card.original_card_id or original_card.id,
                    title=f"{original_card.title} (重写版)",
                    content=rewritten_content,
                    level=original_card.level,
                    parent_card_id=original_card.id,
                    is_original=False,
                    is_active=True,
                    prompt=prompt,
                    model_name=model_name,
                    rewrite_count=0,
                    like=0,
                    dislike=0,
                    collect=0,
                    sort_order=original_card.sort_order,
                    created_by=user_id
                )
                
                session.add(new_card)
                
                # 创建重写记录
                rewrite_record = LogicTreeRewriteModel(
                    card_id=card_id,
                    project_id=project_id,
                    user_id=user_id,
                    prompt=prompt,
                    model_name=model_name,
                    original_content=original_card.content,
                    rewritten_content=rewritten_content,
                    status="completed",
                    created_by=user_id
                )
                
                session.add(rewrite_record)
                
                # 更新原始卡片重写次数
                original_card.rewrite_count += 1
                
                await session.commit()
                
                # 返回新卡片
                return LogicTreeCard(
                    id=new_card.id,
                    project_id=new_card.project_id,
                    # father_project_id=new_card.father_project_id,
                    original_card_id=new_card.original_card_id,
                    title=new_card.title,
                    content=new_card.content,
                    level=new_card.level,
                    parent_card_id=new_card.parent_card_id,
                    is_original=new_card.is_original,
                    is_active=new_card.is_active,
                    rewrite_count=new_card.rewrite_count,
                    like_count=new_card.like,
                    collect_count=new_card.collect,
                    created_at=new_card.created_at,
                    updated_at=new_card.updated_at
                )
                
        except Exception as e:
            logger.error("重写卡片失败", error=str(e), card_id=card_id)
            raise ValueError(f"重写卡片失败: {str(e)}")

    async def get_daily_trending_projects(self, target_count: int = 10) -> Dict[str, Any]:
        """
        获取每日trending项目
        
        Args:
            target_count: 目标项目数量，默认10个
            
        Returns:
            包含trending项目列表的响应数据
        """
        try:
            today = datetime.now(timezone.utc).date()
            
            # 使用锁防止并发请求
            async with LogicTreeService._cache_lock:
                # 检查是否需要刷新缓存
                if LogicTreeService._cache_date != today or not LogicTreeService._daily_trending_cache:
                    logger.info("刷新每日trending缓存", date=today)
                    await self._refresh_daily_trending_cache()
                    LogicTreeService._cache_date = today
            
            # 获取缓存的trending项目
            cached_trending = LogicTreeService._daily_trending_cache.get('projects', [])
            
            # 检查项目分析状态并添加到队列
            analyzed_projects = []
            pending_projects = []
            
            for project in cached_trending:
                analysis_status = await self._check_project_analysis_status(project['html_url'])
                project['status'] = analysis_status['status']
                project['project_id'] = analysis_status.get('project_id')
                project['description_recommend'] = analysis_status.get('description_recommend')
                if analysis_status['status'] == 'completed':
                    analyzed_projects.append(project)
                else:
                    pending_projects.append(project)
                    # 添加到分析队列
                    await self._add_to_analysis_queue(project)
            
            # 如果已分析项目不足目标数量，用高阅读量项目补充
            final_projects = analyzed_projects[:target_count]
            
            if len(final_projects) < target_count:
                needed_count = target_count - len(final_projects)
                popular_projects = await self._get_popular_local_projects(
                    limit=needed_count,
                    exclude_urls=[p['html_url'] for p in final_projects]
                )
                final_projects.extend(popular_projects)
            
            return {
                'projects': final_projects[:target_count],
                'total': len(final_projects),
                'date': today.isoformat(),
                'trending_count': len(analyzed_projects),
                'popular_supplement_count': len(final_projects) - len(analyzed_projects),
                'pending_analysis_count': len(pending_projects)
            }
            
        except Exception as e:
            logger.error("获取每日trending项目失败", error=str(e))
            raise ValueError(f"获取每日trending项目失败: {str(e)}")

    async def _refresh_daily_trending_cache(self) -> None:
        """刷新每日trending缓存"""
        try:
            # 调用GitHub API获取trending项目
            trending_projects = await self._fetch_github_trending()
            
            # 缓存结果到类变量
            LogicTreeService._daily_trending_cache = {
                'projects': trending_projects,
                'fetched_at': datetime.now(timezone.utc).isoformat()
            }
            
            logger.info("成功刷新trending缓存", count=len(trending_projects))
            
        except Exception as e:
            logger.error("刷新trending缓存失败", error=str(e))
            # 如果API调用失败，使用空缓存
            LogicTreeService._daily_trending_cache = {
                'projects': [],
                'fetched_at': datetime.now(timezone.utc).isoformat(),
                'error': str(e)
            }

    async def _fetch_github_trending(self) -> List[Dict[str, Any]]:
        """获取GitHub trending项目"""
        try:
            github_api_base = "https://api.github.com"
            
            # 构建查询参数 - 获取最近1天创建的高星项目
            now = datetime.now(timezone.utc)
            since_date = now - timedelta(days=1)
            
            query = f"created:>={since_date.strftime('%Y-%m-%d')} stars:>10"
            
            url = f"{github_api_base}/search/repositories"
            params = {
                "q": query,
                "sort": "stars",
                "order": "desc",
                "per_page": 30  # 获取30个，后续筛选
            }
            
            headers = {
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'GuguApex-TrendingBot/1.0'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        items = data.get("items", [])
                        
                        # 转换为标准格式
                        projects = []
                        for item in items[:20]:  # 只取前20个
                            try:
                                project = {
                                    'id': str(item["id"]),
                                    'name': item["name"],
                                    'full_name': item["full_name"],
                                    'description': item.get("description", ""),
                                    'html_url': item["html_url"],
                                    'language': item.get("language"),
                                    'stargazers_count': item.get("stargazers_count", 0),
                                    'forks_count': item.get("forks_count", 0),
                                    'created_at': item["created_at"],
                                    'updated_at': item["updated_at"],
                                    'pushed_at': item.get("pushed_at"),
                                    'topics': item.get("topics", []),
                                    'owner': item.get("owner", {}),
                                    'analysis_status': 'pending'
                                }
                                projects.append(project)
                            except Exception as e:
                                logger.error("解析GitHub项目数据失败", error=str(e), item_id=item.get("id"))
                                continue
                        
                        return projects
                    else:
                        logger.error("GitHub API请求失败", status=response.status)
                        return []
                        
        except Exception as e:
            logger.error("获取GitHub trending失败", error=str(e))
            return []

    async def _check_project_analysis_status(self, github_url: str) -> Dict[str, Any]:
        """检查项目分析状态"""
        try:
            async with self.async_session() as session:
                query = select(GitHubProjectModel).where(
                    GitHubProjectModel.repository_url == github_url
                )
                result = await session.execute(query)
                project = result.scalar_one_or_none()
                
                if project:
                    if project.project_phase == ProjectStatusEnum.GenerateSuccess.value:
                        return {
                            'status': 'completed',
                            'project_id': project.id,
                            'description_recommend': project.description_recommend
                        }
                    elif project.project_phase in [
                        ProjectStatusEnum.Generate.value,
                        ProjectStatusEnum.WAIT_GENERATE.value,
                        ProjectStatusEnum.DOWNLOADING.value,
                        ProjectStatusEnum.WAIT_DOWNLOAD.value
                    ]:
                        return {
                            'status': 'analyzing',
                            'project_id': project.id,
                            'description_recommend': project.description_recommend
                        }
                    else:
                        return {
                            'status': 'pending',
                            'project_id': project.id,
                            'description_recommend': project.description_recommend
                        }
                else:
                    return {'status': 'pending'}
                    
        except Exception as e:
            logger.error("检查项目分析状态失败", error=str(e), github_url=github_url)
            return {'status': 'pending'}

    async def _add_to_analysis_queue(self, project: Dict[str, Any]) -> None:
        """添加项目到下载队列（先下载再分析）"""
        try:
            github_url = project['html_url']
            
            # 检查项目是否已存在
            async with self.async_session() as session:
                query = select(GitHubProjectModel).where(
                    GitHubProjectModel.repository_url == github_url
                )
                result = await session.execute(query)
                existing_project = result.scalar_one_or_none()
                
                if not existing_project:
                    # 创建新项目记录
                    new_project = GitHubProjectModel(
                        name=project['name'],
                        repository_url=github_url,
                        description_project=project.get('description', ''),
                        tags=project.get('topics', []),
                        stars=str(project.get('stargazers_count', 0)),
                        project_phase=ProjectStatusEnum.NOT_DOWNLOAD.value,  # 使用枚举
                        status=ProjectStatusEnum.DRAFT.value  # 使用枚举
                    )
                    session.add(new_project)
                    await session.commit()
                    await session.refresh(new_project)  # 刷新以获取ID
                    
                    # 添加到下载队列
                    from app.services.github.github_downloader import GitHubDownloader
                    await GitHubDownloader.add_to_download_queue(
                        "",  # user_id 为空，系统自动添加
                        new_project.id,
                        github_url
                    )
                    logger.info("项目已添加到下载队列", 
                              project_name=project['name'], 
                              project_id=new_project.id)
                          
                elif existing_project.project_phase in [ProjectStatusEnum.NOT_DOWNLOAD.value, ProjectStatusEnum.download_failed.value]:
                    # 重新添加到下载队列
                    from app.services.github.github_downloader import GitHubDownloader
                    await GitHubDownloader.add_to_download_queue(
                        "",
                        existing_project.id,
                        github_url
                    )
                    logger.info("项目重新添加到下载队列", 
                              project_name=project['name'], 
                              project_id=existing_project.id)
                else:
                    a = 1
                    return
                          
        except Exception as e:
            logger.error("添加项目到下载队列失败", error=str(e), project_name=project.get('name'))

    async def _get_popular_local_projects(self, limit: int = 10, exclude_urls: List[str] = None) -> List[Dict[str, Any]]:
        """获取本地高阅读量项目"""
        try:
            exclude_urls = exclude_urls or []
            
            async with self.async_session() as session:
                # 查询高阅读量的已完成项目
                query = select(GitHubProjectModel).where(
                    and_(
                        # GitHubProjectModel.project_phase == ProjectStatusEnum.GenerateSuccess.value,
                        GitHubProjectModel.status == ProjectStatusEnum.TRUE.value,
                        #  ~GitHubProjectModel.repository_url.in_(exclude_urls) if exclude_urls else True
                    )
                ).order_by(
                    desc(GitHubProjectModel.read_count),
                    desc(GitHubProjectModel.collect_count),
                    desc(GitHubProjectModel.created_at)
                ).limit(limit)
                
                result = await session.execute(query)
                projects = result.scalars().all()
                
                # 转换为标准格式
                popular_projects = []
                for project in projects:
                    project_data = {
                        'id': project.id,
                        'name': project.name,
                        'full_name': project.name,
                        'description': project.description_project or project.description_recommend or '',
                        'html_url': project.repository_url,
                        'language': None,
                        'stargazers_count': int(project.stars) if project.stars and project.stars.isdigit() else 0,
                        'forks_count': 0,
                        'created_at': project.created_at.isoformat(),
                        'updated_at': project.updated_at.isoformat(),
                        'pushed_at': project.updated_at.isoformat(),
                        'topics': project.tags or [],
                        'owner': {},
                        'analysis_status': 'completed',
                        'analysis_project_id': project.id,
                        'read_count': project.read_count or 0,
                        'collect_count': project.collect_count or 0,
                        'is_local_popular': True
                    }
                    popular_projects.append(project_data)
                
                logger.info("获取本地热门项目", count=len(popular_projects))
                return popular_projects
                
        except Exception as e:
            logger.error("获取本地热门项目失败", error=str(e))
            return []

# @GeneratedBy:AI
# 在文件末尾添加以下新的Schema定义

class LogicTreeCardTreeNew(BaseModel):
    """逻辑树卡片新的树形结构 - 单一子节点版本"""
    card: LogicTreeCard = Field(..., description="卡片信息")
    children: Optional['LogicTreeCardTreeNew'] = Field(None, description="单个最新子卡片")
    history_keywords: List[str] = Field(default=[], description="历史关键词列表（来自所有历史版本的prompt）")