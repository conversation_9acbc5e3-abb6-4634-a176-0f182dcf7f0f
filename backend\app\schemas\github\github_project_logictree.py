# @GeneratedBy:AI
"""
逻辑树相关Schema
"""
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class LogicTreeCardBase(BaseModel):
    """逻辑树卡片基础模型"""
    title: str = Field(..., description="卡片标题")
    content: str = Field(..., description="卡片内容")
    level: int = Field(default=0, description="层级深度")
    parent_card_id: Optional[str] = Field(None, description="父卡片ID")
    is_original: bool = Field(default=False, description="是否为原始卡片")


class LogicTreeCardCreate(LogicTreeCardBase):
    """逻辑树卡片创建模型"""
    project_id: str = Field(..., description="项目ID")
    original_card_id: Optional[str] = Field(None, description="原始卡片ID")


class LogicTreeCardUpdate(BaseModel):
    """逻辑树卡片更新模型"""
    title: Optional[str] = Field(None, description="卡片标题")
    content: Optional[str] = Field(None, description="卡片内容")
    is_active: Optional[bool] = Field(None, description="是否激活")


class LogicTreeCard(LogicTreeCardBase):
    """逻辑树卡片模型"""
    id: str = Field(..., description="卡片ID")
    project_id: str = Field(..., description="项目ID")
    original_card_id: Optional[str] = Field(None, description="原始卡片ID")
    parent_card_id: Optional[str] = Field(None, description="父卡片ID")
    is_active: bool = Field(default=True, description="是否激活")
    rewrite_count: int = Field(default=0, description="重写次数")
    like_count: int = Field(default=0, description="点赞数")
    collect_count: int = Field(default=0, description="收藏数")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class LogicTreeRewriteCreate(BaseModel):
    """逻辑树重写创建模型"""
    card_id: str = Field(..., description="卡片ID")
    project_id: str = Field(..., description="项目ID")
    prompt: str = Field(..., description="重写提示词")
    model_name: str = Field(..., description="使用的AI模型")
    original_content: str = Field(..., description="原始内容")


class LogicTreeRewrite(LogicTreeRewriteCreate):
    """逻辑树重写模型"""
    id: str = Field(..., description="重写记录ID")
    user_id: str = Field(..., description="用户ID")
    rewritten_content: str = Field(..., description="重写后内容")
    status: str = Field(default="completed", description="重写状态")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class LogicTreeProjectCardsRequest(BaseModel):
    """获取项目逻辑树请求模型"""
    project_id: str = Field(..., description="项目ID")


class LogicTreeModelListRequest(BaseModel):
    """获取模型列表请求模型"""
    project_id: str = Field(..., description="项目ID")


class LogicTreeRewriteRequest(BaseModel):
    """重写请求模型"""
    card_id: str = Field(..., description="卡片ID")
    project_id: str = Field(..., description="项目ID")
    prompt: str = Field(..., description="重写提示词")
    model_name: str = Field(..., description="使用的AI模型")


class LogicTreeRewriteListRequest(BaseModel):
    """重写列表请求模型"""
    page: int = Field(default=1, description="页码")
    page_size: int = Field(default=10, description="每页大小")


class LogicTreeCardTree(BaseModel):
    """逻辑树卡片树形结构"""
    card: LogicTreeCard = Field(..., description="卡片信息")
    children: List['LogicTreeCardTree'] = Field(default=[], description="子卡片列表")


class LogicTreeRewriteListResponse(BaseModel):
    """重写列表响应模型"""
    items: List[Dict[str, Any]] = Field(..., description="重写记录列表")
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")


class LogicTreeModelInfo(BaseModel):
    """AI模型信息"""
    name: str = Field(..., description="模型名称")
    display_name: str = Field(..., description="显示名称")
    description: str = Field(..., description="模型描述")
    is_available: bool = Field(default=True, description="是否可用")


class LogicTreeCardTreeNew(BaseModel):
    """逻辑树卡片新的树形结构 - 单一子节点版本"""
    card: LogicTreeCard = Field(..., description="卡片信息")
    children: Optional['LogicTreeCardTreeNew'] = Field(None, description="单个最新子卡片")
    history_keywords: List[str] = Field(default=[], description="历史关键词列表（来自所有历史版本的prompt）")
