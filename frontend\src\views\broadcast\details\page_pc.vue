<template>
  <div class="home">
    <div class="mask" style=""></div>
    <div class="top">
      <div class="width">
        <div class="project-detail">
          <div style="display: flex">
            <div class="project-icon">
              <img
                :src="projectDetail.icon_url"
                style="object-fit: contain"
                v-if="projectDetail.icon_url" />
              <div v-else class="text">
                {{ formatName(projectDetail.name) }}
              </div>
            </div>
            <div class="project-desc">
              <div style="display: flex; align-items: center">
                {{ formatName(projectDetail.name) }}
                <div class="meta-favorite" @click.stop="handleCollect">
                  <Button1
                    class="meta-favorite-icon"
                    width="18px"
                    height="18px"
                    gray
                    :filled="projectDetail.is_collected"></Button1>
                  <span class="meta-favorite-text">
                    {{ formatStar(projectDetail.collect_count) || "--" }}
                  </span>
                </div>
              </div>
              <div v-html="projectDetail.description_recommend"></div>
              <div
                style="display: flex; align-items: center"
                v-if="projectDetail?.tags?.length">
                <div class="tags-title" style="">项目标签：</div>
                <div class="tags">
                  <div
                    class="tags-name"
                    v-for="item in projectDetail.tags"
                    :key="item"
                    @click="handleTags(item)">
                    {{ item }}
                  </div>
                </div>
              </div>
              <div class="github-link" @click="handleLink">
                <img src="@/assets/images/github2.png" />
                <span>
                  {{ projectDetail.repository_url }}
                </span>
                <img
                  src="@/assets/images/icon02.png"
                  style="margin: 2px 4px 0 16px" />
                <span style="font-size: 14px; color: #60848d">
                  {{ formatStar(projectDetail.stars) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="project-introduction">
      <div class="width" style="align-items: center">
        <div
          :style="{
            marginRight: '24px',
            fontWeight: activeTabIndex === index ? 'bold' : 'normal',
            display: 'flex',
            alignItems: 'center',
          }"
          v-for="(item, index) in tabName"
          :key="item.name"
          @click="handleTab(index)">
          <img
            :src="item.imgUrl"
            style="width: 20px; height: 20px; margin-right: 3px" />
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="project-directory">
      <div class="width">
        <template v-if="showFlod">
          <div class="left" v-if="activeTabIndex === 1">
            <div class="code-left-top">
              <div @click="showMermaid">
                <div>{{ formatName(projectDetail.name) }}</div>
              </div>
              <el-input
                ref="queryIptRef"
                v-model="queryIpt"
                style="width: 130px; position: absolute; right: 0px"
                placeholder="搜索文件"
                v-if="iptStatus"
                @input="onQueryChanged">
                <template #suffix>
                  <el-icon
                    class="el-input__icon"
                    style="color: rgb(168, 171, 178)">
                    <search />
                  </el-icon>
                </template>
              </el-input>
              <el-icon
                @click="showIpt"
                v-else
                class="el-input__icon"
                style="cursor: pointer; color: #999eae">
                <search />
              </el-icon>
            </div>
            <el-auto-resizer style="height: calc(100% - 60px); overflow: auto">
              <template #default="{ height, width }">
                <el-tree
                  ref="treeRef"
                  id="el-tree-v2"
                  class="el-tree-v2"
                  style="margin: 20px 0 0px 0; max-width: 200px"
                  :data="projTree"
                  :props="props"
                  :height="height"
                  :filter-method="filterMethod"
                  @node-click="handleFileTree">
                  <template #default="{ node, data }">
                    <div style="display: flex; align-items: center">
                      <img
                        style="width: 18px; height: 18px"
                        :src="
                          '/icons/' +
                          (data.type === 'dir'
                            ? getIconForFolder(node.label)
                            : getIconForFile(node.label))
                        " />
                      <div
                        style="
                          margin-left: 5px;
                          width: 140px;
                          white-space: nowrap;
                          overflow: hidden;
                          text-overflow: ellipsis;
                        "
                        :style="{
                          marginLeft: '5px',
                          color:
                            activeFileTreeLabel === node.data.path
                              ? '#FE7300'
                              : '#656464',
                        }"
                        :title="node.label">
                        {{ node.label }}
                      </div>
                    </div>
                  </template>
                </el-tree>
              </template>
            </el-auto-resizer>
          </div>
        </template>
        <div class="right" v-if="activeTabIndex === 1">
          <img
            v-if="showFlod"
            class="right-flod"
            src="@/assets/images/right.png"
            @click="handleFlod(false)" />
          <img
            v-if="showFlod === false"
            class="left-flod"
            src="@/assets/images/right.png"
            @click="handleFlod(true)" />
          <div v-if="codeText === ''" class="right-container">
            <template
              v-if="
                projectDetail.dependency_mermaid &&
                projectDetail?.dependency_mermaid.includes('暂无依赖数据')
              ">
              <div
                style="
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  height: 100%;
                ">
                <div class="no-data" style="">暂无依赖数据</div>
              </div>
            </template>
            <template v-else>
              <MermaidPreview v-if="text" :text="text"></MermaidPreview>
            </template>
          </div>
          <div
            style="display: flex; align-items: center"
            v-if="codeText !== ''">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item v-for="item in pathList" :key="item">
                {{ item }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div v-show="codeText !== ''" class="code-md">
            <div>
              <span style="margin-right: 10px"></span>
              <span>{{ formatFileSize(fileSize) }}</span>
            </div>
            <MarkdownPreview
              :text="codeText"
              height="calc(100vh - 274px)"></MarkdownPreview>
          </div>
        </div>
        <div v-else-if="activeTabIndex === 0" class="right article-right">
          <div
            class="right-container"
            style="display: flex; padding: 60px 0px"
            ref="containerRef">
            <el-backtop
              :right="40"
              :bottom="40"
              :visibility-height="200"
              target=".article-container"
              @click="backTop" />
            <div
              class="article-container"
              ref="article-container"
              @scroll="scroll"
              style="scrollbar-gutter: stable">
              <div
                v-for="(item, index) in list"
                :key="'right' + item.id"
                class="article">
                <div
                  class="title"
                  :id="`title${item.id}`"
                  @click="articleFlod(index, item.id, 'normal')">
                  <div>{{ item.title }}</div>
                  <div class="line" style=""></div>
                  <img
                    class="article-img"
                    :id="`image${item.id}`"
                    :ref="(ele) => imgsList.push(ele)"
                    src="@/assets/images/flod.png" />
                  <el-popover
                    placement="bottom"
                    :visible="visibleArr[index]"
                    :width="380"
                    popper-class="ai-popover">
                    <template #reference>
                      <div
                        @click.stop="handleAiPrompt(item, index)"
                        style="
                          margin-left: 14px;
                          height: 20px;
                          display: flex;
                          align-items: center;
                        ">
                        <GradientText
                          text="AI"
                          :colors="['#55C4D5', '#2C666F']"
                          :angle="180" />
                      </div>
                    </template>
                    <div>
                      <div>
                        <el-select
                          v-model="modelName"
                          placeholder="请选择ai模型"
                          style="width: 130px; margin-right: 6px">
                          <el-option
                            v-for="item in modelList"
                            :key="item.display_name"
                            :label="item.display_name"
                            :value="item.display_name" />
                        </el-select>
                        <el-select
                          v-model="history_keywords"
                          placeholder="查看历史提示词"
                          style="width: 180px">
                          <el-option
                            v-for="(item, index) in item.history_keywords"
                            :key="index"
                            :label="item"
                            :value="index" />
                        </el-select>
                      </div>
                      <el-input
                        placeholder="请输入提示词"
                        v-model="aiPrompt"
                        class="textarea"
                        style="width: 356px; margin-top: 4px"
                        :rows="6"
                        type="textarea"
                        @focus="handleFocus($event)" />
                      <div style="display: flex; flex-direction: row-reverse">
                        <button
                          @click.stop="handleAiConfirm(item, index)"
                          style="
                            width: 82px;
                            height: 28px;
                            background: #f0f0f0;
                            border-radius: 4px 4px 4px 4px;
                            border: none;
                            cursor: pointer;
                            margin-top: 4px;
                          ">
                          <span>确认</span>
                        </button>
                      </div>
                    </div>
                  </el-popover>
                </div>
                <MarkdownPreview
                  :id="`${item.id}`"
                  class="content art-content"
                  style="color: #64738b; padding: 0 26px 0 15px"
                  :text="
                    index === 0 &&
                    projectDetail.dependency_mermaid &&
                    !projectDetail?.dependency_mermaid.includes('暂无依赖数据')
                      ? item.content + '\n ### 依赖关系图解'
                      : item.content
                  "></MarkdownPreview>
                <template
                  v-if="
                    index === 0 &&
                    projectDetail.dependency_mermaid &&
                    !projectDetail?.dependency_mermaid.includes(
                      '暂无依赖数据'
                    ) &&
                    showMamerid
                  ">
                  <div style="border: 1px solid #ccc; margin: 0 26px 20px 15px">
                    <MermaidPreview
                      v-if="projectDetail.dependency_mermaid"
                      :text="projectDetail.dependency_mermaid"
                      height="600px"></MermaidPreview>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
        <div v-else-if="activeTabIndex === 2" class="right article-right">
          <div
            class="right-container licence"
            style="display: flex"
            ref="containerRef">
            <div class="article-container licence-article">
              <div style="padding: 0 25px">
                <MarkdownPreview
                  class="content"
                  :text="licnce[0]?.content"></MarkdownPreview>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <HomeFooter style="background-color: transparent"></HomeFooter>
    <Share
      style="position: fixed; right: 40px; bottom: 60px"
      :project="projectDetail"></Share>
    <WxQrCode />
  </div>
</template>

<script setup name="BroadcastDetails">
import HomeFooter from "@/layout/components/HomeFooter/index.vue";
import { getIconForFile, getIconForFolder } from "vscode-icons-js";
import WxQrCode from "@/components/WxQrCode";
import { Search } from "@element-plus/icons-vue";
import { onClickOutside, useScroll, useThrottleFn } from "@vueuse/core";
import MarkdownPreview from "@/components/MarkdownPreview";
import MermaidPreview from "@/components/MermaidPreview";
import codeImg from "@/assets/images/code.png";
import summaryImg from "@/assets/images/summary.png";
import licenceImg from "@/assets/images/licence.png";
import {
  getProjectTree,
  getProjectFile,
  getProjectCardTree,
  addUserCollect,
  deleteUserCollect,
  getAiModelList,
  getCardRewrite,
} from "@/api/broadcast";
import { getFileExtension, formatFileSize } from "@/utils/validate";
import { formatStar } from "@/utils";
import { getToken } from "@/utils/auth";
import GradientText from "@/views/broadcast/components/GradientText.vue";
import Share from "@/components/Share/index.vue";
import { nextTick } from "vue";
import useUserStore from "@/store/modules/user";
import Button1 from "@/components/Button/index1.vue";
import { ref } from "vue";
const { proxy } = getCurrentInstance();
const userStore = useUserStore();
const { query } = useRoute();
const artsList = ref([]);
const imgsList = ref([]);
const queryIptRef = useTemplateRef("queryIptRef");
const treeRef = useTemplateRef("treeRef");
const articleContainer = useTemplateRef("article-container");
const isScrolling = ref(false);

const history_keywords = ref("");
const aiPrompt = ref("");
const visibleArr = ref(Array(10).fill(false));
const router = useRouter();

const options = ref([
  {
    value: "1",
    label: "选项1",
  },
  {
    value: "2",
    label: "选项2",
  },
  {
    value: "3",
    label: "选项3",
  },
]);

/**
 * 概述滚动直接滚动到底部
 */
const scroll = useThrottleFn(() => {
  if (!isScrolling.value) {
    document.getElementsByClassName("app-main")[0].scroll({ top: 999 });
  }
}, 1000);

/**
 * 点击外部取消焦点
 */
onClickOutside(queryIptRef, () => {
  queryIpt.value = "";
  iptStatus.value = false;
  treeRef.value.filter(queryIpt.value);
  treeRef.value.setExpandedKeys([]);
});
// 项目文件列表
const projTree = ref([]);

//项目详情
const projectDetail = ref({});

/**
 * 获取项目文件列表
 */
const projectTree = () => {
  getProjectTree({
    project_id: query.id,
    depth: "10",
  }).then((res) => {
    if (res.code === 200) {
      projTree.value = res.data.tree;
      projectDetail.value = res.data.project;
      text.value = res.data.project.dependency_mermaid ?? "";
    }
  });
};

const containerRef = ref(null);

// 展示mamerid图
const showMamerid = ref(true);

/**
 * 概述标题折叠
 * @param index
 * @param id
 * @param type
 */
const articleFlod = (index, id, type) => {
  if (index === 0) {
    showMamerid.value = !showMamerid.value;
  }
  if (type === "normal") {
    const flodDom = document.getElementById(id);
    if (flodDom.className === "content art-content none") {
      flodDom.className = "content art-content";
      imgsList.value[index].style.transform = "rotate(0)";
    } else {
      flodDom.className = "content art-content none";
      imgsList.value[index].style.transform = "rotate(-90deg)";
    }
  } else {
    const flodDom = document.getElementById(id);
    flodDom.className = "content art-content";
    imgsList.value[index].style.transform = "rotate(0)";
  }
};
// 文件详情
const codeText = ref("");
// 面包屑路径
const pathList = ref([]);
// 依赖图
let text = ref("");

// 树组件属性
const props = {
  value: "path",
  label: "name",
  children: "children",
};

//概述tab
const tabName = ref([]);
// 概述激活的tab
const activeTabIndex = ref(0);

// 文件大小
const fileSize = ref(0);

// 激活的文件树名称
const activeFileTreeLabel = ref("");

/**
 * 点击文件树进行详情展示
 * @param data
 * @param node
 */
const handleFileTree = (data, node) => {
  if (node.data.type === "dir") {
    return;
  }
  if (activeFileTreeLabel.value === node.data.path) {
    return;
  }

  if (node.data.type !== "dir") {
    activeFileTreeLabel.value = node.data.path;
    fileSize.value = node.data.size;
  }

  pathList.value = node.data.path.split("\/");
  getProjectFile({
    project_id: query.id,
    file_path: node.data.path,
  }).then((res) => {
    if (res.code === 200) {
      if (getFileExtension(node.data.path.toLowerCase()) === "md") {
        codeText.value = res.data.content;
      } else {
        codeText.value = "```\n" + res.data.content + "\n```";
      }
    }
  });
};

/**
 * 格式化项目名
 * @param name 项目名
 */
const formatName = (name) => {
  return name?.replace(/\/+$/, "").split("/").pop();
};

/**
 * 折叠左侧菜单
 * @param index
 */
const handleTab = (index) => {
  artsList.value = [];
  imgsList.value = [];
  activeTabIndex.value = index;
  showFlod.value = !(activeTabIndex.value === 2);
  codeText.value = "";
  activeFileTreeLabel.value = "";
  const timer = setTimeout(() => {
    if (index === 0) {
      initArtTitle();
      articleContainer.value.scroll({ top: 0 });
    }

    if (activeTabIndex.value === 1) {
      document.getElementById("el-tree-v2").addEventListener("scroll", scroll);
    }
    clearTimeout(timer);
  }, 300);
};

/**
 * 展示mermaid图
 */
const showMermaid = () => {
  codeText.value = "";
  activeFileTreeLabel.value = "";
};
const showFlod = ref(true);

const handleFlod = (flag) => {
  showFlod.value = flag;
};

// 概述列表
const list = ref([]);
// 许可证信息
const licnce = ref([]);

/**
 * 获取概述列表
 */
let cardList = [];
let cardList2 = [];
const getCard = async () => {
  const res = await getProjectCardTree({
    project_id: query.id,
  });

  if (res.code === 200) {
    if (res.data.length) {
      cardList = res.data.map((item) => {
        if (item.children) {
          return {
            ...item.children.card,
            history_keywords: item.history_keywords,
          };
        }
        return {
          ...item.card,
          history_keywords: item.history_keywords,
        };
      });
      console.log(cardList, "========");

      licnce.value = cardList.filter((item) => item.title === "许可证");
      list.value = structuredClone(
        cardList.filter((item) => item.title !== "许可证")
      );
      cardList2 = structuredClone(
        cardList.filter((item) => item.title !== "许可证")
      );
    }
    if (licnce.value.length) {
      tabName.value = [
        {
          name: "概述",
          imgUrl: summaryImg,
        },
        {
          name: "代码",
          imgUrl: codeImg,
        },
        {
          name: "许可证",
          imgUrl: licenceImg,
        },
      ];
    } else {
      tabName.value = [
        {
          name: "概述",
          imgUrl: summaryImg,
        },
        {
          name: "代码",
          imgUrl: codeImg,
        },
      ];
    }
  }
};

// 文件树搜索
const queryIpt = ref("");

/**
 * 搜索文件
 */
const onQueryChanged = () => {
  treeRef.value.filter(queryIpt.value);
};
const filterMethod = (query, node) => node.name.includes(query);

const iptStatus = ref(false);
const showIpt = () => {
  iptStatus.value = true;
};

const contentList = ref([]);

/**
 * 刚进入展开第一个概述标题
 */
const initArtTitle = () => {
  nextTick(() => {
    contentList.value = Array.from(
      document.getElementsByClassName("art-content")
    );
    contentList.value.forEach((ele, index) => {
      if (index < 1) {
        ele.className = "content art-content";
      } else {
        ele.className = "content art-content none";
      }
    });
    imgsList.value.forEach((ele, index) => {
      if (index < 1) {
        ele.style.transform = "rotate(0)";
      } else {
        ele.style.transform = "rotate(-90deg)";
      }
    });
  });
};

/**
 * 替换掉md里边一些不能高亮的语法
 * @param input md字符串
 * @param search 高亮的关键词
 */
function replaceModuleInMarkdown(input, search) {
  if (!input || !search) return input;
  // const copyInput = structuredClone(input);
  // 存储所有需要保护的内容
  const protectedParts = [];

  // 保护函数 - 将匹配到的内容替换为占位符
  const protect = (text, pattern) => {
    return text.replace(pattern, (match) => {
      const placeholder = `__PROTECTED_${protectedParts.length}__`;
      protectedParts.push(match);
      return placeholder;
    });
  };

  // 保护各种Markdown元素
  let protectedText = structuredClone(input);

  // 保护图片 ![alt](url)
  protectedText = protect(protectedText, /!\[([^\]]*)\]\([^)]*\)/g);
  // 保护链接 [text](url)
  protectedText = protect(protectedText, /\[([^\]]*)\]\([^)]*\)/g);
  // 保护内联代码块 `code`
  protectedText = protect(protectedText, /`[^`]+`/g);

  // 高亮搜索词
  search.split(" ").forEach((word) => {
    if (word.trim()) {
      protectedText = protectedText.replaceAll(word, `==${word}==`);
    }
  });

  // 恢复被保护的内容
  protectedParts.forEach((part, index) => {
    protectedText = protectedText.replace(`__PROTECTED_${index}__`, part);
  });

  return protectedText;
}

const modelList = ref([]);
const modelName = ref("");
const getAiModel = async () => {
  const res = await getAiModelList({
    project_id: query.id,
  });
  if (res.code === 200) {
    modelList.value = res.data;
  }
};

const handleAiConfirm = (item, index) => {
  getCardRewrite({
    card_id: item.id,
    project_id: item.project_id,
    prompt: aiPrompt.value,
    model_name: modelName.value,
    rewrite_text: selectText.value,
  }).then((res) => {
    if (res.code === 200) {
      list.value[index] = res.data;
      cardList2[index] = structuredClone(res.data);
    }
  });
};

const selectText = ref("");
const handleFocus = (event) => {
  if (window.getSelection().toString()) {
    selectText.value = window.getSelection().toString();
  }
  if (selectText.value) {
    zIndex.value = 20;
    cardList = structuredClone(cardList2);
    list.value = cardList.map((item) => {
      item.content = replaceModuleInMarkdown(item.content, selectText.value);
      return item;
    });
  } else {
    proxy.$modal.msgWarning("请选择概述里的一段文字");
  }
};
onMounted(async () => {
  await getCard();
  initArtTitle();
  projectTree();
  getAiModel();
  // 进入页面如果有需要高亮的关键词 则进行高亮处理
  const timer = setTimeout(() => {
    if (window.location.hash) {
      const imageDom = document.getElementById(
        `image${window.location.hash.split("#")[1]}`
      );
      articleFlod(imageDom, window.location.hash.split("#")[1], "link");
    }
    if (query.cardIds) {
      const cardIds = query.cardIds.split(",");
      cardIds.forEach((id) => {
        if (id) {
          const cardDom = document.getElementById(id);
          cardDom.className = "content art-content";
          const imgDom = document.getElementById("image" + id);
          imgDom.style.transform = "rotate(0)";
          list.value = list.value.map((item) => {
            if (item.id === id) {
              item.content = replaceModuleInMarkdown(
                item.content,
                query.search
              );
            }
            return item;
          });
        }
      });
      query.search.split(" ").forEach((word) => {
        projectDetail.value.description_recommend =
          projectDetail.value.description_recommend.replaceAll(
            word,
            `<mark>${word}</mark>`
          );
      });
    }

    timer && clearTimeout(timer);
  }, 300);
});

/**
 * 跳转github链接
 */
const handleLink = () => {
  window.open(projectDetail.value.repository_url);
};

/**
 * 回到概述顶部
 */
const backTop = () => {
  articleContainer.value.scroll({ top: 0, behavior: "smooth" });
  isScrolling.value = true;
  const timer = setTimeout(() => {
    isScrolling.value = false;
    clearTimeout(timer);
  }, 1000);
};

const handleAiPrompt = (item, index) => {
  if (!getToken()) {
    proxy.$modal.msgWarning("登录后即可添加ai提示词");
    return;
  }
  visibleArr.value[index] = !visibleArr.value[index];
  setTimeout(() => {
    aiPrompt.value = "";
  }, 500);
  zIndex.value = -10;
  list.value = cardList2;
};
/**
 * 用户收藏
 */
const handleCollect = async () => {
  // 判断用户是否登录，如果为登录，则提示用户去登录
  if (!userStore?.name) {
    proxy.$modal.msgWarning("登录后即可收藏");
    return;
  }
  let res = {};
  if (projectDetail.value.is_collected) {
    res = await deleteUserCollect({ project_id: projectDetail.value.id });
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已取消收藏");
      projectDetail.value.is_collected = !projectDetail.value.is_collected;
      projectDetail.value.collect_count--;
    }
  } else {
    res = await addUserCollect({ project_id: projectDetail.value.id });
    if (res.code === 200) {
      proxy.$modal.msgSuccess("已收藏");
      projectDetail.value.is_collected = !projectDetail.value.is_collected;
      projectDetail.value.collect_count++;
    }
  }
};
/**
 * 标签跳转到首页进行搜索
 * @param item
 */
const handleTags = (item) => {
  router.push({
    path: "/",
    query: { search: item },
  });
};

const zIndex = ref(-10);
</script>

<style scoped lang="scss">
::v-deep(.el-input) {
  --el-input-focus-border-color: #fe730080;
}

::v-deep(.el-affix) {
  padding: 0 14px;
}
img {
  width: 100%;
  height: 100%;
}

::v-deep(code) {
  max-height: calc(100vh - 346px) !important;
  .md-editor-code-block {
    overflow: visible !important;
  }
}

::v-deep(.el-breadcrumb) {
  margin: 19px 0;
  .el-breadcrumb__separator {
    margin: 0 5px;
  }
  .is-link {
    font-weight: 800;
    font-size: 12px;
    color: #5b97b5;
    &:hover {
      color: #5b97b5;
    }
  }

  .el-breadcrumb__item:last-child {
    .is-link {
      color: #000;
    }
  }
}
::v-deep(.el-anchor) {
  --el-color-primary: #ff9500;
  /* 锚点容器整体样式 */
  .el-anchor__marker {
    width: 5px;
    height: 5px;
    border-radius: 50%;
  }
}

::v-deep(.el-backtop) {
  --el-backtop-bg-color: transparent !important;
  position: absolute;
  right: 40px;
  bottom: 40px;
}

.home {
  position: relative;
  font-size: 14px;
  background:
    radial-gradient(at 20% 0%, rgba(202, 229, 245, 0.6), transparent 20%),
    radial-gradient(at 60% -15%, rgba(202, 229, 245, 0.7), transparent 40%),
    radial-gradient(at 100% 100%, rgba(160, 235, 248, 0.7), transparent 40%),
    radial-gradient(at 0% 100%, rgba(243, 252, 202, 1), transparent 30%);
  .width {
    display: flex;
    width: 98%;
  }
  @media (min-width: 1200px) {
    .width {
      display: flex;
      max-width: var(--content-max-width);
      min-width: var(--content-max-width);
    }
  }

  .top {
    width: 100%;
    height: 142px;
    background: #eef0f1;
    box-shadow: 0px 5px 5px 0px #e8f6f7;
    border-radius: 0px 0px 0px 0px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    .project-detail {
      margin-left: 10px;
      display: flex;
      align-items: center;

      .project-icon {
        width: 60px;
        height: 60px;
        border-radius: 4px 4px 4px 4px;
        overflow: hidden;
        padding: 10px;
        box-sizing: border-box;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;

        .text {
          font-size: 14px;
          width: 70%;
          text-align: center;
          color: #1d1c1c;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .project-desc {
        flex: 1;
        margin: 0 11px;

        & > div:nth-child(1) {
          font-weight: 800;
          font-size: 18px;
          color: #3d3d3d;
          line-height: 18px;
        }
        & > div:nth-child(2) {
          font-weight: 400;
          font-size: 14px;
          color: #3f4a54;
          margin-top: 10px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          letter-spacing: 1px;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .tags-title {
          font-size: 12px;
          color: #60848d;
          width: 63px;
        }
        .tags {
          display: flex;
          margin-top: 4px;
          flex-wrap: wrap;
          gap: 4px 16px;
          flex: 1;

          .tags-name {
            padding: 2px 4px 3px 4px;
            border-radius: 2px;
            background-color: #d9ecf0;
            text-align: center;
            line-height: 1;
            color: #4c6d7d;
            font-size: 12px;
            height: fit-content;
            cursor: pointer;
          }
        }

        .github-link {
          width: fit-content;
          margin-top: 10px;
          display: flex;
          align-items: center;

          img {
            width: 18px;
            height: 18px;
            margin-right: 8px;
          }

          span {
            font-size: 14px;
            color: #60848d;
            cursor: pointer;
          }
        }
      }
    }
  }
  .project-introduction {
    height: 40px;
    font-weight: normal;
    font-size: 14px;
    color: #656464;
    box-sizing: border-box;
    background: #fff;
    display: flex;
    justify-content: center;
    border-bottom: 1px solid rgb(232, 232, 232);
    & > .width > div {
      cursor: pointer;
    }
  }
  .project-directory {
    display: flex;
    padding-bottom: 20px;
    display: flex;
    justify-content: center;
    min-height: 620px;
    height: calc(100vh - 150px);

    .left {
      width: 220px;
      min-width: 220px;
      padding: 14px;
      box-sizing: border-box;
      background-color: #fff;
      height: 100%;
      border: 1px solid rgb(232, 232, 232);
      border-top: none;
      .el-tree-v2 {
        ::v-deep(.el-tree-node) {
          width: fit-content;
        }
      }

      .code-left-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 32px;
        font-weight: 800;
        font-size: 16px;
        color: rgb(255, 149, 0);
        position: relative;

        & > div {
          display: flex;
          align-items: center;
          cursor: pointer;

          & > div {
            width: 100px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .article-left-top {
        font-weight: 800;
        font-size: 16px;
        color: #ff9500;
        margin-bottom: 11px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;

        & > div {
          width: 100px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }

    .right {
      max-width: 1188px;
      min-height: 600px;
      width: 728px;
      flex: 1;
      height: 100%;
      margin-left: 20px;
      border-radius: 10px;
      box-sizing: border-box;
      position: relative;
      ::v-deep(.md-editor-preview) {
        font-size: 14px;
      }
      .code-md {
        background-color: #fff;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #e8e8e8;
        padding: 15px;
        position: relative;
      }
      .article-container {
        width: 100%;
        overflow: auto;
        padding: 0 25px;

        .article {
          margin-bottom: 15px;

          .title {
            position: sticky;
            top: 0;
            z-index: 90;
            height: 26px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-left: 4px solid #55c4d5;
            padding-left: 12px;
            font-weight: bold;
            font-size: 16px;
            color: #000000;
            background-color: #fff;
            cursor: pointer;
          }
        }

        .none {
          display: none;
        }
        .line {
          flex: 1;
          height: 1px;
          border-bottom: 1px dashed #c4c4c4;
          margin: 0 6px;
        }
        .article-img {
          width: 20px;
          height: 20px;
          cursor: pointer;
          transform: rotate(-90deg);
        }
      }

      .left-flod {
        width: 30px;
        height: 30px;
        position: absolute;
        left: -23px;
        top: calc(50% - 17px);
        transform: translateY(-50%) rotate(180deg);
        cursor: pointer;
        z-index: 15;
      }
      .right-flod {
        width: 30px;
        height: 30px;
        position: absolute;
        left: -27px;
        top: calc(50% - 17px);
        transform: translateY(-50%);
        cursor: pointer;
        z-index: 15;
      }

      .right-container {
        height: calc(100% - 15px);
        background-color: #fff;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #e8e8e8;
        padding: 15px 25px;
        margin-top: 15px;
        position: relative;
        overflow: auto;
        box-sizing: border-box;

        .no-data {
          width: 300px;
          height: 100px;
          border: 2px solid #9370db;
          background-color: #ececff;
          text-align: center;
          line-height: 100px;
          font-size: 26px;
          color: #333333;
        }
      }
      .licence {
        padding: 20px 0;

        .licence-article {
          padding: 0;
        }
      }
    }

    .article-right {
      margin: 0;
    }
  }
}
.meta-favorite {
  margin-left: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 14px;
  font-weight: normal;
  color: #646464;
  height: 100%;
  width: fit-content;
  cursor: pointer;
  .meta-favorite-icon {
    width: 18px;
    height: 18px;
    margin-right: 4px;
  }
}
.mask {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: transparent;
  z-index: v-bind(zIndex);
}
</style>

<style lang="scss">
// button {
//   & > span:hover {
//     transform: scale(1.05);
//     transition: transform 0.3s ease;
//   }
// }
.ai-popover {
  z-index: 50;
  .el-select__wrapper {
    background-color: #f0f0f0;
    box-shadow: none !important;
  }
  .textarea {
    textarea {
      resize: none;
      box-shadow: none;
    }
  }
}
</style>
