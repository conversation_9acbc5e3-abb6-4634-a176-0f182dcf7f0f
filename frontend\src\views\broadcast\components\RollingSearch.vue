<template>
  <div class="search-container">
    <div
      class="search-wrapper"
      :class="{
        focused: isFocused,
      }">
      <!-- 彩色边框动画层 -->
      <div class="border-animation" :class="{ focused: isFocused }">
        <div class="gradient-border"></div>
      </div>

      <div class="content-wrapper">
        <div class="search-input-box">
          <el-input
            v-model="inputValue"
            ref="searchInput"
            class="search-input"
            clearable
            @keyup.enter="handleSearch"
            :placeholder="placeholder"
            @focus="handleFocus"
            @blur="handleBlur"
            @input="handleInput"
            @clear="handleClear"
            @click="handleClick">
            <template #suffix>
              <i class="el-icon-search el-input__icon" />
            </template>
          </el-input>
          <!-- 自定义placeholder动画 -->
          <div v-if="!inputValue && !isFocused" class="placeholder-wrapper">
            <div
              v-for="(text, index) in placeholders"
              :key="index"
              :class="[
                'placeholder-text',
                { active: currentPlaceholderIndex === index },
              ]">
              {{ text }}
            </div>
          </div>
          <div class="icon-group">
            <button
              v-show="props.showAddProject"
              class="icon-btn"
              @click.stop="handleIcon1Click">
              <img class="search-icon" src="@/assets/images/add.png" />
            </button>
            <button class="icon-btn" @click.stop="handleSearch">
              <img class="search-icon" src="@/assets/images/search.png" />
            </button>
          </div>
        </div>

        <div
          v-show="device === 'pc' && showHistory && historyList.length > 0"
          class="history-dropdown">
          <div class="history-wapper">
            <div
              v-for="(item, index) in historyList"
              :key="index"
              class="history-item" @click="selectHistory(item)">
              <span class="history-text">
                {{ item }}
              </span>
              <button @click.stop="deleteHistory(index)" class="delete-btn">
                <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                  <path
                    d="M1 1L13 13M13 1L1 13"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from "vue";
import useAppStore from "@/store/modules/app";
const emit = defineEmits(["search", "click", "clear", "focus", "addProject"]);
const props = defineProps({
  showAddProject: {
    type: Boolean,
    default: true,
  },
  // 是否点击跳转模式，用于移动端首页，只有点击事件，不执行搜索逻辑
  clickMode: {
    type: Boolean,
    default: false,
  },
});
const device = computed(() => useAppStore().device);
let inputValue = defineModel("searchKey");

const searchInput = ref(null);
const isFocused = ref(false);
const currentPlaceholderIndex = ref(0);
const historyList = ref([]);
const showHistory = ref(false);

// 占位符文字列表
const placeholders = [
  "Python教程",
  "JavaScript框架",
  "机器学习入门",
  "Git命令",
  "Docker部署",
  "ChatGPT API",
  "MySQL索引",
  "TypeScript类型",
  "深度学习",
  "Redis缓存",
  "AI绘画",
  "正则表达式",
  "Linux命令",
  "数据结构",
  "神经网络",
  "响应式设计",
  "数据库优化",
];
// 当前搜索框input显示的
let placeholder = ref("");
// 搜索框上面滚动的
const currentPlaceholder = computed(() => {
  return placeholders[currentPlaceholderIndex.value];
});

let placeholderInterval = null;

// 开始占位符滚动动画
const startPlaceholderAnimation = () => {
  placeholderInterval = setInterval(() => {
    currentPlaceholderIndex.value =
      (currentPlaceholderIndex.value + 1) % placeholders.length;
  }, 3000);
};

// 停止占位符滚动动画
const stopPlaceholderAnimation = () => {
  if (placeholderInterval) {
    clearInterval(placeholderInterval);
    placeholderInterval = null;
  }
};

// 获取历史记录
const getHistory = () => {
  const history = localStorage.getItem("searchHistory");
  if (history) {
    historyList.value = JSON.parse(history);
  }
};

// 保存历史记录
const saveHistory = (keyword) => {
  if (!keyword.trim()) return;

  let history = [...historyList.value];
  // 移除重复项
  history = history.filter((item) => item !== keyword);
  // 添加到开头
  history.unshift(keyword);
  // 最多保存10条
  if (history.length > 10) {
    history = history.slice(0, 10);
  }

  historyList.value = history;
  localStorage.setItem("searchHistory", JSON.stringify(history));
};

// 删除历史记录
const deleteHistory = (index) => {
  historyList.value.splice(index, 1);
  localStorage.setItem("searchHistory", JSON.stringify(historyList.value));

  // 如果删除后没有历史记录了，隐藏下拉框
  if (historyList.value.length === 0) {
    showHistory.value = false;
  }
};

// 处理获得焦点
const handleFocus = () => {
  emit("focus");
  isFocused.value = true;
  showHistory.value = true;
  stopPlaceholderAnimation();

  // 如果输入框为空
  if (!inputValue.value) {
    placeholder.value = currentPlaceholder.value;
    // 选中全部文本
    // setTimeout(() => {
    //   searchInput.value.select();
    // }, 0);
  }
};

// 处理失去焦点
const handleBlur = () => {
  isFocused.value = false;
  showHistory.value = false;
  placeholder.value = "";
  if (!inputValue.value) {
    startPlaceholderAnimation();
  }
};

const handleInput = () => {
  // 输入时可以添加搜索逻辑
};
const handleClear = () => {
  placeholder.value = currentPlaceholder.value;
  isFocused.value = true;
  emit("clear");
};
const handleClick = () => {
  emit("click");
};
// 选择历史记录
const selectHistory = (item) => {
  console.log(222);

  inputValue.value = item;
  showHistory.value = false;
  emit("search");
};

// 图标点击事件
const handleSearch = () => {
  handleBlur();
  if (props.clickMode) {
    emit("search");
    return;
  }
  // 搜索按钮逻辑
  if (inputValue.value) {
    saveHistory(inputValue.value);
    emit("search");
  } else {
    inputValue.value = currentPlaceholder.value;
    saveHistory(currentPlaceholder.value);
    emit("search");
  }
};

const handleIcon1Click = () => {
  emit("addProject");
};

onMounted(() => {
  getHistory();
  startPlaceholderAnimation();
});

onUnmounted(() => {
  stopPlaceholderAnimation();
});
</script>

<style lang="scss" scoped>
.search-container {
  display: inline-block;
  position: sticky;
  top: 10px;
  z-index: 10;
  width: 100%;
  margin-bottom: 50px;
}

.search-wrapper {
  position: absolute;
  width: 80vw;
  max-width: 800px;
  transition: all 0.3s ease;
  left: 50%;
  transform: translateX(-50%);
}

// 彩色边框动画
.border-animation {
  position: absolute;
  border-radius: 22px;
  overflow: hidden;
  z-index: 0;
  pointer-events: none;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;

  .gradient-border {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2000px;
    height: 2000px;
    background: conic-gradient(
      from 0deg,
      #b993e1 0deg,
      #6ac5f6 90deg,
      #b993e1 180deg,
      #f37fea 260deg,
      #5bbef8 270deg,
      #f37fea 280deg,
      #b993e1 360deg
    );
    transform: translate(-50%, -50%);
  }
  &.focused {
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    .gradient-border {
      background: conic-gradient(
        from 0deg,
        #b993e1 0deg,
        #6ac5f6 90deg,
        #b993e1 180deg,
        #f37fea 210deg,
        #b993e1 240deg,
        #5bbef8 270deg,
        #b993e1 300deg,
        #f37fea 330deg,
        #b993e1 360deg
      );
      animation: rotate 3s linear infinite;
    }
  }
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.content-wrapper {
  position: relative;
  z-index: 1;
  background: white;
  border-radius: 20px;
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;

  .focused & {
    border-color: transparent;
  }
}

.search-input-box {
  position: relative;
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 16px;
  background: transparent;
  padding-right: 10px;
  position: relative;
  z-index: 2;
}

// 自定义placeholder动画
.placeholder-wrapper {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  height: 20px;
  overflow: hidden;
  pointer-events: none;
  width: calc(100% - 120px);
}

.placeholder-text {
  position: absolute;
  left: 0;
  top: 0;
  color: #999;
  font-size: 16px;
  white-space: nowrap;
  transform: translateY(100%);
  opacity: 0;
  transition:
    transform 0.5s ease-out,
    opacity 0.5s ease-out;

  &.active {
    transform: translateY(0);
    opacity: 1;
  }
}

.icon-group {
  display: flex;
  align-items: center;
  gap: 2px;
  position: relative;
  z-index: 2;
}

.icon-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  color: #666;
  .search-icon {
    width: 28px;
    height: 28px;
    opacity: 0.86;
  }
  &:hover {
    .search-icon {
      opacity: 1;
    }
  }
}

.history-dropdown {
  border-top: 1px solid #f0f0f0;
  border-radius: 0 0 22px 22px;
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
  .history-wapper {
    max-height: 253px;
    overflow-y: auto;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 253px;
  }
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 20px;
  transition: background 0.2s ease;

  &:hover {
    background: #f5f5f5;
  }

  &:last-child {
    border-radius: 0 0 22px 22px;
  }
}

.history-text {
  flex: 1;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 10px;
}

.delete-btn {
  width: 20px;
  height: 20px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  color: #999;
  flex-shrink: 0;

  &:hover {
    background: #f0f0f0;
    color: #ff4444;
  }

  &:active {
    transform: scale(0.9);
  }
}

// 滚动条样式
.history-dropdown::-webkit-scrollbar {
  width: 6px;
}

.history-dropdown::-webkit-scrollbar-track {
  background: transparent;
}

.history-dropdown::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 3px;

  &:hover {
    background: #ccc;
  }
}
:deep(.search-input .el-input__inner) {
  font-size: 16px;
  color: #333;
}
:deep(.search-input .el-input__wrapper) {
  padding: 0;
}
</style>
