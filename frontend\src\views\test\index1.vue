<template>
  <div>
    <el-input type="text" v-model="emoji1"></el-input>
    <EmojiPicker
      :hide-search="true"
      :native="true"
      @select="onSelectEmoji"
      :disabled-groups="[
        'travel_places',
        'flags',
        'symbols',
        'objects',
        'activities',
      ]"
      :hide-group-names="true" />

    <div v-for="(item, index) in arr" :key="index">
      <el-popover
        :visible="visible[index]"
        placement="bottom"
        title="Title"
        :width="200"
        content="this is content, this is content, this is content">
        <template #reference>
          <el-button class="m-2" @click="visible[index] = !visible[index]">
            Manual to activate
          </el-button>
        </template>
      </el-popover>
      <!-- <input
        type="text"
        v-for="(item, index) in arr"
        :key="index"
        v-model="arr[index]"
        :placeholder="`请输入第${index + 1}个值`" /> -->
    </div>
    <el-popover
      :visible="visible1"
      placement="bottom"
      title="Title"
      :width="200"
      content="this is content, this is content, this is content">
      <template #reference>
        <el-button class="m-2" @click="visible1 = !visible1">
          Manual to activate555
        </el-button>
      </template>
    </el-popover>
    <div style="margin-top: 20px">
      <h3>当前数组值:</h3>
      <div v-for="(item, index) in arr" :key="index">
        索引 {{ index }}: {{ item }}
      </div>
    </div>
  </div>
</template>

<script setup>
// import picker compopnent
import EmojiPicker from "vue3-emoji-picker";
import { ref } from "vue";

const visible = ref(Array(5).fill(false));
const visible1 = ref(false);

const arr = ref(["值0", "值1", "值2", "值3", "值4"]);

// import css
import "vue3-emoji-picker/css";
const emoji1 = ref("");
function onSelectEmoji(emoji) {
  console.log(emoji);
  emoji1.value = emoji.i;
  console.log(emoji1.value, " emoji.value emoji.value");

  /*
    // result
    {
        i: "😚",
        n: ["kissing face"],
        r: "1f61a", // with skin tone
        t: "neutral", // skin tone
        u: "1f61a" // without tone
    }
    */
}
</script>
<style scoped lang="scss">
:deep(.v3-emoji-picker .v3-footer) {
  display: none;
}
</style>
