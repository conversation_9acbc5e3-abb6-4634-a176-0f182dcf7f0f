<template>
  <div
    class="ranking-list"
    :class="{ 'is-bevel': isBevel, 'is-pc': device === 'pc' }">
    <!-- 头部 -->
    <div class="ranking-list__header">
      <div class="ranking-list__title-wrapper">
        <img
          class="ranking-list__icon"
          src="@/assets/images/icon36.png"
          alt="" />
        <h3 class="ranking-list__title">{{ title }}</h3>
      </div>
      <el-icon
        v-if="isCollapsedVisible"
        size="18"
        class="ranking-list__toggle-icon"
        :class="{ 'is-collapsed': isCollapsed }"
        @click="toggleDisplay">
        <ArrowDownBold />
      </el-icon>
    </div>

    <!-- 榜单内容 -->
    <ul class="ranking-list__content">
      <li
        v-for="(item, index) in displayedItems"
        :key="item.id || index"
        class="ranking-list__item">
        <span class="ranking-list__rank">
          <template v-if="index < 3">
            <img
              class="ranking-list__rank-icon"
              :src="getRankIcon(index)"
              alt="" />
          </template>
          <template v-else>
            {{ index + 1 }}
          </template>
        </span>
        <a
          :href="displayedUrl(item)"
          target="_blank"
          rel="noopener noreferrer"
          class="ranking-list__item-content">
          {{ item.name || item.title }}
        </a>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import useAppStore from "@/store/modules/app";
import rank1 from "@/assets/images/icon37.png";
import rank2 from "@/assets/images/icon38.png";
import rank3 from "@/assets/images/icon39.png";
const device = computed(() => useAppStore().device);

// Props定义
const props = defineProps({
  // 榜单标题
  title: {
    type: String,
    default: "榜单",
  },
  // 榜单数据 - 每项应包含 content/title 和 url
  items: {
    type: Array,
    required: true,
    default: () => [],
  },
  // 最大显示条数
  maxItems: {
    type: Number,
    default: 10,
  },
  // 折叠时显示的条数
  collapsedCount: {
    type: Number,
    default: 3,
  },
  // 是否有斜边，用于首页卡片样式
  isBevel: {
    type: Boolean,
    default: false,
  },
  // 是否显示收起按钮
  isCollapsedVisible: {
    type: Boolean,
    default: true,
  },
});

// 是否折叠状态
const isCollapsed = ref(false);

// 切换显示状态
const toggleDisplay = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 计算显示的条目
const displayedItems = computed(() => {
  const limit = isCollapsed.value ? props.collapsedCount : props.maxItems;
  return props.items.slice(0, limit);
});
// 要跳转的链接
const displayedUrl = (item) => {
  return window.location.origin + "/broadcast/details?id=" + item.project_id;
};

// 获取排名图标
const getRankIcon = (index) => {
  const icons = [rank1, rank2, rank3];
  return icons[index];
};
</script>

<style lang="scss" scoped>
.ranking-list {
  background-color: #fff;
  border-radius: 6px;
  padding: 10px;
  position: relative;
  transition: all 0.3s;
  filter: drop-shadow(0 0 1px #ccc);

  .ranking-list__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 6px;
    margin-bottom: 6px;
    border-bottom: 1px solid #f0f0f0;
  }

  .ranking-list__title-wrapper {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .ranking-list__icon {
    width: 24px;
    height: 24px;
    display: block;
  }

  .ranking-list__title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
  }

  .ranking-list__toggle-icon {
    font-size: 20px;
    font-style: normal;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
    user-select: none;

    &:hover {
      color: #409bff;
    }

    &.is-collapsed {
      transform: rotate(-90deg);
    }
  }

  .ranking-list__content {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .ranking-list__item {
    display: flex;
    align-items: center;
    padding: 4px 0;

    &:hover {
      background-color: #f8f8f8;
    }
  }

  .ranking-list__rank {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
    font-size: 14px;
    color: #666;
  }

  .ranking-list__rank-icon {
    width: 26px;
    height: 26px;
  }

  .ranking-list__item-content {
    flex: 1;
    font-size: 14px;
    color: #525252;
    line-height: 1.5;
    text-decoration: none;
    transition: color 0.3s ease;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &:hover {
      color: #409bff;
      text-decoration: underline;
    }
  }
}
.ranking-list.is-bevel {
  padding: 10px 10px 0;
  margin-top: 10px;
  border-radius: 16px;
  &.is-pc:hover {
    filter: drop-shadow(0 0 4px #bbb);
    transform: scale(1.05);
  }
  &::before {
    content: "";
    border-radius: 16px;
    position: absolute;
    top: -10px;
    left: 0;
    width: 100%;
    height: 60px;
    z-index: -1;
    // background-color: var(--backgroundColor);
    background-color: #fff;
    transform: skewY(2deg);
    transform-origin: bottom left;
  }
  &::after {
    content: "";
    border-radius: 16px;
    position: absolute;
    bottom: -14px;
    left: 0;
    width: 100%;
    height: 60px;
    z-index: -1;
    // background-color: var(--backgroundColor);
    background-color: #fff;
    transform: skewY(4deg);
    transform-origin: bottom left;
  }
}
</style>
